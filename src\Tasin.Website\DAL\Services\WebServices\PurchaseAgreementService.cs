using AutoMapper;
using ClosedXML.Excel;
using LinqKit;
using System.IO.Compression;
using System.Text;
using Tasin.Website.Common.CommonModels;
using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Common.Enums;
using Tasin.Website.Common.Helper;
using Tasin.Website.Common.Services;
using Tasin.Website.Common.Util;
using Tasin.Website.DAL.Interfaces;
using Tasin.Website.DAL.Services.AuthorPredicates;
using Tasin.Website.DAL.Services.WebInterfaces;
using Tasin.Website.Domains.DBContexts;
using Tasin.Website.Domains.Entitites;
using Tasin.Website.Models.SearchModels;
using Tasin.Website.Models.ViewModels;

namespace Tasin.Website.DAL.Services.WebServices
{
    public class PurchaseAgreementService : BaseService<PurchaseAgreementService>, IPurchaseAgreementService
    {
        private readonly IMapper _mapper;
        private readonly ICommonService _commonService;
        private readonly EmailService _emailService;
        private IPurchaseAgreementRepository _purchaseAgreementRepository;
        private IPurchaseAgreementItemRepository _purchaseAgreementItemRepository;
        private IPurchaseOrderRepository _purchaseOrderRepository;
        private IPurchaseOrderItemRepository _purchaseOrderItemRepository;
        private IVendorRepository _vendorRepository;
        private IProductRepository _productRepository;
        private IUnitRepository _unitRepository;
        private IProduct_VendorRepository _productVendorRepository;
        private ICustomerRepository _customerRepository;

        // OPTIMIZED: Cache for parsed vendors to avoid repeated parsing
        private List<VendorOptionViewModel>? _cachedParsedVendors;
        private DateTime _vendorCacheTime = DateTime.MinValue;
        private readonly TimeSpan _vendorCacheExpiry = TimeSpan.FromMinutes(5);

        public PurchaseAgreementService(
            ILogger<PurchaseAgreementService> logger,
            IConfiguration configuration,
            IHttpContextAccessor httpContextAccessor,
            ICurrentUserContext currentUserContext,
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            SampleDBContext dbContext,
            IMapper mapper,
            ICommonService commonService,
            EmailService emailService,
            IPurchaseAgreementRepository purchaseAgreementRepository,
            IPurchaseAgreementItemRepository purchaseAgreementItemRepository,
            IPurchaseOrderRepository purchaseOrderRepository,
            IPurchaseOrderItemRepository purchaseOrderItemRepository,
            IVendorRepository vendorRepository,
            IProductRepository productRepository,
            IUnitRepository unitRepository,
            IProduct_VendorRepository productVendorRepository,
            ICustomerRepository customerRepository) : base(logger, configuration, userRepository, roleRepository, httpContextAccessor, currentUserContext, dbContext)
        {
            _mapper = mapper;
            _commonService = commonService;
            _emailService = emailService;
            _purchaseAgreementRepository = purchaseAgreementRepository;
            _purchaseAgreementItemRepository = purchaseAgreementItemRepository;
            _purchaseOrderRepository = purchaseOrderRepository;
            _purchaseOrderItemRepository = purchaseOrderItemRepository;
            _vendorRepository = vendorRepository;
            _productRepository = productRepository;
            _unitRepository = unitRepository;
            _productVendorRepository = productVendorRepository;
            _customerRepository = customerRepository;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<PurchaseAgreementViewModel>>>> GetPurchaseAgreementList(PurchaseAgreementSearchModel searchModel)
        {
            var ack = new Acknowledgement<JsonResultPaging<List<PurchaseAgreementViewModel>>>();
            try
            {
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);

                // Only get active purchase agreements
                predicate = predicate.And(p => p.IsActive);

                // Apply search filters
                if (!string.IsNullOrWhiteSpace(searchModel.SearchString))
                {
                    var searchValue = searchModel.SearchString.Trim().ToLower();
                    predicate = predicate.And(p => p.Code.ToLower().Contains(searchValue) ||
                                                   p.GroupCode.ToLower().Contains(searchValue));
                }

                if (searchModel.Vendor_ID.HasValue)
                {
                    predicate = predicate.And(p => p.Vendor_ID == searchModel.Vendor_ID.Value);
                }

                if (searchModel.Status.HasValue)
                {
                    predicate = predicate.And(p => p.Status == searchModel.Status.Value.ToString());
                }

                // Apply author predicate
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var purchaseAgreementQuery = await _purchaseAgreementRepository.ReadOnlyRespository.GetWithPagingAsync(
                    filter: predicate,
                    orderBy: q => q.OrderByDescending(p => p.CreatedDate),
                    paging: new PagingParameters(searchModel.PageNumber, searchModel.PageSize)
                );

                var purchaseAgreementViewModels = _mapper.Map<List<PurchaseAgreementViewModel>>(purchaseAgreementQuery.Data);

                if (purchaseAgreementViewModels.Any())
                {
                    // OPTIMIZED: Get all vendor and user IDs in one go
                    var vendorIds = purchaseAgreementViewModels.Select(p => p.Vendor_ID).Distinct().ToList();
                    var createdByIds = purchaseAgreementViewModels.Select(p => p.CreatedBy).Distinct().ToList();

                    // FIXED: Sequential execution to avoid DbContext threading issues
                    var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                        filter: v => vendorIds.Contains(v.ID)
                    );
                    var users = await _userRepository.ReadOnlyRespository.GetAsync(
                        filter: u => createdByIds.Contains(u.Id)
                    );

                    // OPTIMIZED: Create lookup dictionaries for O(1) access
                    var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);
                    var userLookup = users.ToDictionary(u => u.Id, u => u);

                    // OPTIMIZED: Single loop to populate both vendor and user names
                    foreach (var purchaseAgreement in purchaseAgreementViewModels)
                    {
                        if (vendorLookup.TryGetValue(purchaseAgreement.Vendor_ID, out var vendor))
                        {
                            purchaseAgreement.VendorName = vendor.Name;
                        }

                        if (userLookup.TryGetValue(purchaseAgreement.CreatedBy, out var user))
                        {
                            purchaseAgreement.UpdatedByName = user.Name;
                        }
                    }
                }

                ack.Data = new JsonResultPaging<List<PurchaseAgreementViewModel>>
                {
                    Data = purchaseAgreementViewModels,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    Total = purchaseAgreementQuery.TotalRecords
                };
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy danh sách đơn tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error getting purchase agreement list");
            }
            return ack;
        }

        public async Task<Acknowledgement<PurchaseAgreementViewModel>> GetPurchaseAgreementById(int purchaseAgreementId)
        {
            var ack = new Acknowledgement<PurchaseAgreementViewModel>();
            try
            {
                var purchaseAgreement = await _purchaseAgreementRepository.ReadOnlyRespository.FindAsync(purchaseAgreementId);
                if (purchaseAgreement == null)
                {
                    ack.AddMessage("Không tìm thấy đơn tổng hợp.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.ID == purchaseAgreementId);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                if (!authorizedPurchaseAgreements.Any())
                {
                    ack.AddMessage("Bạn không có quyền xem đơn tổng hợp này.");
                    return ack;
                }

                var purchaseAgreementViewModel = _mapper.Map<PurchaseAgreementViewModel>(purchaseAgreement);

                // OPTIMIZED: Get purchase agreement items first to determine what other data we need
                var purchaseAgreementItems = await _purchaseAgreementItemRepository.GetByPurchaseAgreementIdAsync(purchaseAgreementId);
                var purchaseAgreementItemViewModels = _mapper.Map<List<PurchaseAgreementItemViewModel>>(purchaseAgreementItems);

                if (purchaseAgreementItemViewModels.Any())
                {
                    // OPTIMIZED: Collect all IDs needed for batch queries
                    var productIds = purchaseAgreementItemViewModels.Select(p => p.Product_ID).Distinct().ToList();
                    var unitIds = purchaseAgreementItemViewModels.Where(p => p.Unit_ID.HasValue).Select(p => p.Unit_ID.Value).Distinct().ToList();

                    // FIXED: Sequential execution to avoid DbContext threading issues
                    var vendor = await _vendorRepository.ReadOnlyRespository.FindAsync(purchaseAgreement.Vendor_ID);
                    var products = await _productRepository.ReadOnlyRespository.GetAsync(filter: p => productIds.Contains(p.ID));
                    var units = unitIds.Any() ? await _unitRepository.ReadOnlyRespository.GetAsync(filter: u => unitIds.Contains(u.ID)) : new List<Unit>();
                    var createdByUser = await _userRepository.ReadOnlyRespository.FindAsync(purchaseAgreement.CreatedBy);

                    // OPTIMIZED: Create lookup dictionaries for O(1) access
                    var productLookup = products.ToDictionary(p => p.ID, p => p);
                    var unitLookup = units.ToDictionary(u => u.ID, u => u);

                    // Set vendor name
                    if (vendor != null)
                    {
                        purchaseAgreementViewModel.VendorName = vendor.Name;
                    }

                    // Set created by name
                    if (createdByUser != null)
                    {
                        purchaseAgreementViewModel.UpdatedByName = createdByUser.Name;
                    }

                    // OPTIMIZED: Single loop to populate product and unit names using lookups
                    foreach (var item in purchaseAgreementItemViewModels)
                    {
                        if (productLookup.TryGetValue(item.Product_ID, out var product))
                        {
                            item.ProductName = product.Name;
                        }

                        if (item.Unit_ID.HasValue && unitLookup.TryGetValue(item.Unit_ID.Value, out var unit))
                        {
                            item.UnitName = unit.Name;
                        }
                    }
                }
                else
                {
                    // FIXED: Sequential execution for vendor and user info
                    var vendor = await _vendorRepository.ReadOnlyRespository.FindAsync(purchaseAgreement.Vendor_ID);
                    var createdByUser = await _userRepository.ReadOnlyRespository.FindAsync(purchaseAgreement.CreatedBy);

                    if (vendor != null)
                    {
                        purchaseAgreementViewModel.VendorName = vendor.Name;
                    }

                    if (createdByUser != null)
                    {
                        purchaseAgreementViewModel.UpdatedByName = createdByUser.Name;
                    }
                }

                purchaseAgreementViewModel.PurchaseAgreementItems = purchaseAgreementItemViewModels;

                ack.Data = purchaseAgreementViewModel;
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy thông tin đơn tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error getting purchase agreement by id");
            }
            return ack;
        }

        public async Task<Acknowledgement> UpdatePurchaseAgreement(PurchaseAgreementViewModel postData)
        {
            var ack = new Acknowledgement();
            try
            {
                if (postData.Id == 0)
                {
                    // Create new purchase agreement
                    var newPurchaseAgreement = _mapper.Map<Purchase_Agreement>(postData);
                    newPurchaseAgreement.Code = await Generator.GenerateEntityCodeAsync(EntityPrefix.PurchaseAgreement, DbContext);
                    newPurchaseAgreement.CreatedDate = DateTime.Now;
                    newPurchaseAgreement.CreatedBy = CurrentUserId;
                    newPurchaseAgreement.UpdatedDate = newPurchaseAgreement.CreatedDate;
                    newPurchaseAgreement.UpdatedBy = newPurchaseAgreement.CreatedBy;

                    // Calculate total price
                    newPurchaseAgreement.TotalPrice = postData.PurchaseAgreementItems.Sum(item => (item.Price ?? 0) * item.Quantity);

                    await ack.TrySaveChangesAsync(res => res.AddAsync(newPurchaseAgreement), _purchaseAgreementRepository.Repository);

                    if (ack.IsSuccess && postData.PurchaseAgreementItems.Any())
                    {
                        await SavePurchaseAgreementItems(newPurchaseAgreement.ID, postData.PurchaseAgreementItems);
                    }
                }
                else
                {
                    // Update existing purchase agreement
                    var existingPurchaseAgreement = await _purchaseAgreementRepository.ReadOnlyRespository.FindAsync(postData.Id);
                    if (existingPurchaseAgreement == null)
                    {
                        ack.AddMessage("Không tìm thấy đơn tổng hợp để cập nhật.");
                        return ack;
                    }

                    // Check author predicate
                    var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                    predicate = predicate.And(p => p.ID == postData.Id);
                    predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                    var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                        filter: predicate
                    );

                    if (!authorizedPurchaseAgreements.Any())
                    {
                        ack.AddMessage("Bạn không có quyền cập nhật đơn tổng hợp này.");
                        return ack;
                    }

                    existingPurchaseAgreement.Vendor_ID = postData.Vendor_ID;
                    existingPurchaseAgreement.GroupCode = postData.GroupCode;
                    existingPurchaseAgreement.Note = postData.Note;
                    existingPurchaseAgreement.Status = postData.Status.ToString();
                    existingPurchaseAgreement.UpdatedDate = DateTime.Now;
                    existingPurchaseAgreement.UpdatedBy = CurrentUserId;

                    // Calculate total price
                    existingPurchaseAgreement.TotalPrice = postData.PurchaseAgreementItems.Sum(item => (item.Price ?? 0) * item.Quantity);

                    await ack.TrySaveChangesAsync(res => res.UpdateAsync(existingPurchaseAgreement), _purchaseAgreementRepository.Repository);

                    if (ack.IsSuccess && postData.PurchaseAgreementItems.Any())
                    {
                        // Delete existing items
                        await _purchaseAgreementItemRepository.DeleteByPurchaseAgreementIdAsync(postData.Id);

                        // Save new items
                        await SavePurchaseAgreementItems(existingPurchaseAgreement.ID, postData.PurchaseAgreementItems);
                    }
                }
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lưu đơn tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error creating or updating purchase agreement");
            }
            return ack;
        }

        public async Task<Acknowledgement> DeletePurchaseAgreementById(int purchaseAgreementId)
        {
            var ack = new Acknowledgement();
            try
            {
                var purchaseAgreement = await _purchaseAgreementRepository.ReadOnlyRespository.FindAsync(purchaseAgreementId);
                if (purchaseAgreement == null)
                {
                    ack.AddMessage("Không tìm thấy đơn tổng hợp để xóa.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.ID == purchaseAgreementId);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate
                );

                if (!authorizedPurchaseAgreements.Any())
                {
                    ack.AddMessage("Bạn không có quyền xóa đơn tổng hợp này.");
                    return ack;
                }

                // Soft delete by setting IsActive to false
                purchaseAgreement.IsActive = false;
                purchaseAgreement.UpdatedDate = DateTime.Now;
                purchaseAgreement.UpdatedBy = CurrentUserId;

                await ack.TrySaveChangesAsync(res => res.UpdateAsync(purchaseAgreement), _purchaseAgreementRepository.Repository);
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi xóa đơn tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error deleting purchase agreement");
            }
            return ack;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<PAGroupViewModel>>>> GetPAGroupList(PAGroupSearchModel searchModel)
        {
            var ack = new Acknowledgement<JsonResultPaging<List<PAGroupViewModel>>>();
            try
            {
                // Get all purchase agreements grouped by GroupCode
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.IsActive);

                // Apply search filters
                if (!string.IsNullOrWhiteSpace(searchModel.SearchString))
                {
                    var searchValue = searchModel.SearchString.Trim().ToLower();
                    predicate = predicate.And(p => p.GroupCode.ToLower().Contains(searchValue));
                }

                if (searchModel.Status.HasValue)
                {
                    predicate = predicate.And(p => p.Status == searchModel.Status.Value.ToString());
                }

                if (!string.IsNullOrWhiteSpace(searchModel.GroupCode))
                {
                    predicate = predicate.And(p => p.GroupCode == searchModel.GroupCode);
                }

                if (searchModel.Vendor_ID.HasValue)
                {
                    predicate = predicate.And(p => p.Vendor_ID == searchModel.Vendor_ID.Value);
                }

                if (searchModel.DateFrom.HasValue)
                {
                    predicate = predicate.And(p => p.CreatedDate >= searchModel.DateFrom.Value);
                }

                if (searchModel.DateTo.HasValue)
                {
                    predicate = predicate.And(p => p.CreatedDate <= searchModel.DateTo.Value);
                }

                // Apply author predicate
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: predicate,
                    orderBy: q => q.OrderByDescending(p => p.CreatedDate)
                );

                // Group by GroupCode to create PA ViewModels
                var groupedPAs = purchaseAgreements.GroupBy(pa => pa.GroupCode).ToList();

                if (groupedPAs.Any())
                {
                    // OPTIMIZED: Get all vendor IDs from all groups at once
                    var allVendorIds = purchaseAgreements.Select(pa => pa.Vendor_ID).Distinct().ToList();
                    var allVendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                        filter: v => allVendorIds.Contains(v.ID)
                    );

                    // OPTIMIZED: Create vendor lookup dictionary for O(1) access
                    var vendorLookup = allVendors.ToDictionary(v => v.ID, v => v);

                    var paViewModels = new List<PAGroupViewModel>();
                    foreach (var group in groupedPAs)
                    {
                        var childPAs = _mapper.Map<List<PurchaseAgreementViewModel>>(group.ToList());

                        // OPTIMIZED: Use lookup dictionary instead of querying for each group
                        foreach (var childPA in childPAs)
                        {
                            if (vendorLookup.TryGetValue(childPA.Vendor_ID, out var vendor))
                            {
                                childPA.VendorName = vendor.Name;
                            }
                        }

                        var paViewModel = new PAGroupViewModel
                        {
                            GroupCode = group.Key,
                            TotalPrice = group.Sum(pa => pa.TotalPrice),
                            Status = CalculatePAGroupStatus(group),
                            CreatedDate = group.Min(pa => pa.CreatedDate),
                            CreatedBy = group.First().CreatedBy,
                            UpdatedDate = group.Max(pa => pa.UpdatedDate),
                            UpdatedBy = group.First().UpdatedBy,
                            ChildPAs = childPAs
                        };

                        paViewModels.Add(paViewModel);
                    }

                    // Apply pagination to grouped results
                    var totalRecords = paViewModels.Count;
                    var pagedResults = paViewModels
                        .Skip((searchModel.PageNumber - 1) * searchModel.PageSize)
                        .Take(searchModel.PageSize)
                        .ToList();

                    ack.Data = new JsonResultPaging<List<PAGroupViewModel>>
                    {
                        Data = pagedResults,
                        PageNumber = searchModel.PageNumber,
                        PageSize = searchModel.PageSize,
                        Total = totalRecords
                    };
                }
                else
                {
                    ack.Data = new JsonResultPaging<List<PAGroupViewModel>>
                    {
                        Data = new List<PAGroupViewModel>(),
                        PageNumber = searchModel.PageNumber,
                        PageSize = searchModel.PageSize,
                        Total = 0
                    };
                }

                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy danh sách PA tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error getting PA list");
            }
            return ack;
        }

        public async Task<Acknowledgement<PAGroupViewModel>> GetPAByGroupCode(string groupCode)
        {
            var ack = new Acknowledgement<PAGroupViewModel>();
            try
            {
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive,
                    orderBy: q => q.OrderBy(pa => pa.Vendor_ID)
                );

                if (!purchaseAgreements.Any())
                {
                    ack.AddMessage("Không tìm thấy PA tổng hợp với GroupCode này.");
                    return ack;
                }

                // Check author predicate
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.GroupCode == groupCode);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPAs = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(filter: predicate);
                if (!authorizedPAs.Any())
                {
                    ack.AddMessage("Bạn không có quyền xem PA tổng hợp này.");
                    return ack;
                }

                var childPAs = _mapper.Map<List<PurchaseAgreementViewModel>>(purchaseAgreements);

                // OPTIMIZED: Get all data needed in batch queries
                var paIds = childPAs.Select(pa => pa.Id).ToList();
                var vendorIds = childPAs.Select(pa => pa.Vendor_ID).Distinct().ToList();

                // FIXED: Sequential execution to avoid DbContext threading issues
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(filter: v => vendorIds.Contains(v.ID));
                var allItems = await _purchaseAgreementItemRepository.ReadOnlyRespository.GetAsync(
                    filter: item => paIds.Contains(item.PA_ID),
                    orderBy: q => q.OrderBy(i => i.PA_ID).ThenBy(i => i.Product_ID)
                );

                // OPTIMIZED: Create lookup dictionaries
                var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);
                var itemsByPAId = allItems.GroupBy(item => item.PA_ID).ToDictionary(g => g.Key, g => g.ToList());

                // OPTIMIZED: Get all product and unit IDs from all items at once
                var allProductIds = allItems.Select(i => i.Product_ID).Distinct().ToList();
                var allUnitIds = allItems.Where(i => i.Unit_ID.HasValue).Select(i => i.Unit_ID!.Value).Distinct().ToList();

                // FIXED: Sequential execution for product and unit queries
                var products = allProductIds.Any() ? await _productRepository.ReadOnlyRespository.GetAsync(filter: p => allProductIds.Contains(p.ID)) : new List<Product>();
                var units = allUnitIds.Any() ? await _unitRepository.ReadOnlyRespository.GetAsync(filter: u => allUnitIds.Contains(u.ID)) : new List<Unit>();

                // OPTIMIZED: Create product and unit lookup dictionaries
                var productLookup = products.ToDictionary(p => p.ID, p => p);
                var unitLookup = units.ToDictionary(u => u.ID, u => u);

                // OPTIMIZED: Single loop to populate all data using lookups
                foreach (var childPA in childPAs)
                {
                    // Set vendor name using lookup
                    if (vendorLookup.TryGetValue(childPA.Vendor_ID, out var vendor))
                    {
                        childPA.VendorName = vendor.Name;
                    }

                    // Set items using lookup
                    if (itemsByPAId.TryGetValue(childPA.Id, out var paItems))
                    {
                        var itemViewModels = _mapper.Map<List<PurchaseAgreementItemViewModel>>(paItems);

                        // Set product and unit names using lookups
                        foreach (var item in itemViewModels)
                        {
                            if (productLookup.TryGetValue(item.Product_ID, out var product))
                            {
                                item.ProductName = product.Name;
                            }

                            if (item.Unit_ID.HasValue && unitLookup.TryGetValue(item.Unit_ID.Value, out var unit))
                            {
                                item.UnitName = unit.Name;
                            }
                        }

                        childPA.PurchaseAgreementItems = itemViewModels;
                    }
                    else
                    {
                        childPA.PurchaseAgreementItems = new List<PurchaseAgreementItemViewModel>();
                    }
                }

                var paViewModel = new PAGroupViewModel
                {
                    GroupCode = groupCode,
                    TotalPrice = purchaseAgreements.Sum(pa => pa.TotalPrice),
                    Status = CalculatePAGroupStatus(purchaseAgreements),
                    CreatedDate = purchaseAgreements.Min(pa => pa.CreatedDate),
                    CreatedBy = purchaseAgreements.First().CreatedBy,
                    UpdatedDate = purchaseAgreements.Max(pa => pa.UpdatedDate),
                    UpdatedBy = purchaseAgreements.First().UpdatedBy,
                    ChildPAs = childPAs
                };

                ack.Data = paViewModel;
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy thông tin PA tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error getting PA by group code");
            }
            return ack;
        }

        public async Task<Acknowledgement<PAGroupViewModel>> GetPAGroupPreview()
        {
            var ack = new Acknowledgement<PAGroupViewModel>();
            try
            {
                // Get all confirmed purchase orders with items in one query
                var confirmedOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: po => po.Status == EPOStatus.Confirmed.ToString() && po.IsActive,
                    includeProperties: "PurchaseOrderItems"
                );

                if (!confirmedOrders.Any())
                {
                    ack.AddMessage("Không có đơn hàng nào ở trạng thái Đã xác nhận để tạo PA tổng hợp.");
                    return ack;
                }

                // Use included items instead of separate query
                var allOrderItems = confirmedOrders.SelectMany(po => po.PurchaseOrderItems).ToList();

                if (!allOrderItems.Any())
                {
                    ack.AddMessage("Không có sản phẩm nào trong các đơn hàng đã xác nhận.");
                    return ack;
                }

                // Get product information for transformation
                var productIds = allOrderItems.Select(poi => poi.Product_ID).Distinct().ToList();
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    filter: p => productIds.Contains(p.ID)
                );
                var productLookup = products.ToDictionary(p => p.ID, p => p);

                // Get parent products for child products that have ParentID
                var childProducts = products.Where(p => p.ParentID.HasValue).ToList();
                var parentProductIds = childProducts.Select(p => p.ParentID!.Value).Distinct().ToList();

                var parentProducts = new List<Product>();
                if (parentProductIds.Any())
                {
                    parentProducts = (await _productRepository.ReadOnlyRespository.GetAsync(
                        filter: p => parentProductIds.Contains(p.ID)
                    )).ToList();

                    // Add parent products to lookup if not already present
                    foreach (var parentProduct in parentProducts)
                    {
                        if (!productLookup.ContainsKey(parentProduct.ID))
                        {
                            productLookup[parentProduct.ID] = parentProduct;
                        }
                    }
                }

                // Transform child products to parent products and calculate quantities with loss rate
                var transformedOrderItems = allOrderItems.Select(item =>
                {
                    var product = productLookup.TryGetValue(item.Product_ID, out var prod) ? prod : null;

                    // If product has a parent, use parent product instead
                    var targetProductId = product?.ParentID ?? item.Product_ID;

                    // Calculate quantity with loss rate for all products that have LossRate
                    var adjustedQuantity = item.Quantity;
                    if (product?.LossRate.HasValue == true)
                    {
                        // Apply loss rate: (100 + LossRate) * Quantity / 100
                        adjustedQuantity = (100 + product.LossRate.Value) * item.Quantity / 100;
                    }

                    return new
                    {
                        OriginalProductId = item.Product_ID,
                        TargetProductId = targetProductId,
                        Quantity = adjustedQuantity,
                        Price = item.Price,
                        Unit_ID = item.Unit_ID,
                        TaxRate = item.TaxRate,
                        ProcessingFee = item.ProcessingFee,
                        POItemId = item.ID
                    };
                }).ToList();

                // Get all target product IDs for vendor relationships
                var allTargetProductIds = transformedOrderItems.Select(i => i.TargetProductId).Distinct().ToList();

                // Get product-vendor relationships for all target products with highest priority vendors
                var productVendors = await _productVendorRepository.GetHighestPriorityVendorsByProductIdsAsync(allTargetProductIds);

                // Create lookup dictionary for better performance - each product maps to its highest priority vendor
                var productVendorLookup = productVendors.ToDictionary(pv => pv.Product_ID, pv => pv.Vendor_ID);

                // Create product-vendor price lookup for getting vendor prices
                var productVendorPriceLookup = productVendors
                    .ToDictionary(pv => $"{pv.Product_ID}_{pv.Vendor_ID}", pv => pv.UnitPrice ?? 0);

                // Group transformed items by vendor using lookup
                var vendorGroups = transformedOrderItems
                    .Where(item => productVendorLookup.ContainsKey(item.TargetProductId))
                    .GroupBy(item => productVendorLookup[item.TargetProductId])
                    .ToDictionary(g => g.Key, g => g.ToList());

                if (!vendorGroups.Any())
                {
                    ack.AddMessage("Không tìm thấy thông tin nhà cung cấp cho các sản phẩm trong đơn hàng.");
                    return ack;
                }

                // Get all vendor information in one query
                var vendorIds = vendorGroups.Keys.ToList();
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                    filter: v => vendorIds.Contains(v.ID)
                );
                var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);

                // Get all unit information for display
                var unitIds = transformedOrderItems.Where(item => item.Unit_ID.HasValue).Select(item => item.Unit_ID.Value).Distinct().ToList();

                var units = await _unitRepository.ReadOnlyRespository.GetAsync(
                    filter: u => unitIds.Contains(u.ID)
                );

                var unitLookup = units.ToDictionary(u => u.ID, u => u);

                var previewChildPAs = new List<PurchaseAgreementViewModel>();

                foreach (var vendorGroup in vendorGroups)
                {
                    var vendorId = vendorGroup.Key;
                    var vendorItems = vendorGroup.Value;

                    if (!vendorLookup.TryGetValue(vendorId, out var vendor))
                        continue;

                    // Group by target product and sum quantities
                    var productGroups = vendorItems.GroupBy(item => item.TargetProductId)
                        .Select(g => new
                        {
                            ProductId = g.Key,
                            TotalQuantity = g.Sum(i => i.Quantity),
                            UnitId = g.First().Unit_ID
                        }).ToList();

                    // Create preview PA items using target products with vendor prices
                    var paItems = productGroups.Select(productGroup =>
                    {
                        // Get vendor price from product-vendor relationship
                        var vendorPrice = productVendorPriceLookup.TryGetValue($"{productGroup.ProductId}_{vendorId}", out var price) ? price : 0;

                        return new PurchaseAgreementItemViewModel
                        {
                            Product_ID = productGroup.ProductId,
                            ProductName = productLookup.TryGetValue(productGroup.ProductId, out var product) ? product.Name : "",
                            Quantity = productGroup.TotalQuantity,
                            Unit_ID = productGroup.UnitId,
                            UnitName = productGroup.UnitId.HasValue && unitLookup.TryGetValue(productGroup.UnitId.Value, out var unit) ? unit.Name : "",
                            Price = vendorPrice // Use vendor price
                        };
                    }).ToList();

                    // Calculate total price using vendor prices
                    var totalPrice = paItems.Sum(item => item.Quantity * item.Price);

                    var previewPA = new PurchaseAgreementViewModel
                    {
                        Code = $"[Preview] PA-{vendor.Name}",
                        Vendor_ID = vendorId,
                        VendorName = vendor.Name,
                        TotalPrice = totalPrice ?? 0,
                        Status = EPAStatus.New,
                        PurchaseAgreementItems = paItems
                    };

                    previewChildPAs.Add(previewPA);
                }

                // Create preview PA Group ViewModel
                var previewPAGroup = new PAGroupViewModel
                {
                    GroupCode = "[Preview] Sẽ được tạo tự động",
                    TotalPrice = previewChildPAs.Sum(pa => pa.TotalPrice),
                    Status = EPAStatus.New,
                    CreatedDate = DateTime.Now,
                    ChildPAs = previewChildPAs
                };

                ack.Data = previewPAGroup;
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy preview PA tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error getting PA group preview");
            }
            return ack;
        }

        public async Task<Acknowledgement<PAGroupViewModel>> CreatePAGroup()
        {
            var ack = new Acknowledgement<PAGroupViewModel>();

            // Use database transaction to ensure data consistency
            using var transaction = await DbContext.Database.BeginTransactionAsync();
            try
            {
                // Get all confirmed purchase orders with items in one query
                var confirmedOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: po => po.Status == EPOStatus.Confirmed.ToString() && po.IsActive,
                    includeProperties: "PurchaseOrderItems"
                );

                if (!confirmedOrders.Any())
                {
                    ack.AddMessage("Không có đơn hàng nào ở trạng thái Đã xác nhận để tạo PA tổng hợp.");
                    return ack;
                }

                // Use included items instead of separate query
                var allOrderItems = confirmedOrders.SelectMany(po => po.PurchaseOrderItems).ToList();

                if (!allOrderItems.Any())
                {
                    ack.AddMessage("Không có sản phẩm nào trong các đơn hàng đã xác nhận.");
                    return ack;
                }

                // Get product information for transformation
                var productIds = allOrderItems.Select(poi => poi.Product_ID).Distinct().ToList();
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    filter: p => productIds.Contains(p.ID)
                );
                var productLookup = products.ToDictionary(p => p.ID, p => p);

                // Get parent products for child products that have ParentID
                var childProducts = products.Where(p => p.ParentID.HasValue).ToList();
                var parentProductIds = childProducts.Select(p => p.ParentID!.Value).Distinct().ToList();

                var parentProducts = new List<Product>();
                if (parentProductIds.Any())
                {
                    parentProducts = (await _productRepository.ReadOnlyRespository.GetAsync(
                        filter: p => parentProductIds.Contains(p.ID)
                    )).ToList();

                    // Add parent products to lookup if not already present
                    foreach (var parentProduct in parentProducts)
                    {
                        if (!productLookup.ContainsKey(parentProduct.ID))
                        {
                            productLookup[parentProduct.ID] = parentProduct;
                        }
                    }
                }

                // Transform child products to parent products and calculate quantities with loss rate
                var transformedOrderItems = allOrderItems.Select(item =>
                {
                    var product = productLookup.TryGetValue(item.Product_ID, out var prod) ? prod : null;

                    // If product has a parent, use parent product instead
                    var targetProductId = product?.ParentID ?? item.Product_ID;

                    // Calculate quantity with loss rate for all products that have LossRate
                    var adjustedQuantity = item.Quantity;
                    if (product?.LossRate.HasValue == true)
                    {
                        // Apply loss rate: (100 + LossRate) * Quantity / 100
                        adjustedQuantity = (100 + product.LossRate.Value) * item.Quantity / 100;
                    }

                    return new
                    {
                        OriginalProductId = item.Product_ID,
                        TargetProductId = targetProductId,
                        Quantity = adjustedQuantity,
                        Price = item.Price,
                        Unit_ID = item.Unit_ID,
                        POItemId = item.ID
                    };
                }).ToList();

                // Get all target product IDs for vendor relationships
                var allTargetProductIds = transformedOrderItems.Select(i => i.TargetProductId).Distinct().ToList();

                // Get product-vendor relationships for all target products with highest priority vendors
                var productVendors = await _productVendorRepository.GetHighestPriorityVendorsByProductIdsAsync(allTargetProductIds);

                // Create lookup dictionary for better performance - each product maps to its highest priority vendor
                var productVendorLookup = productVendors.ToDictionary(pv => pv.Product_ID, pv => pv.Vendor_ID);

                // Create product-vendor price lookup for getting vendor prices
                var productVendorPriceLookup = productVendors
                    .ToDictionary(pv => $"{pv.Product_ID}_{pv.Vendor_ID}", pv => pv.UnitPrice ?? 0);

                // Group transformed items by vendor using lookup
                var vendorGroups = transformedOrderItems
                    .Where(item => productVendorLookup.ContainsKey(item.TargetProductId))
                    .GroupBy(item => productVendorLookup[item.TargetProductId])
                    .ToDictionary(g => g.Key, g => g.ToList());

                if (!vendorGroups.Any())
                {
                    ack.AddMessage("Không tìm thấy thông tin nhà cung cấp cho các sản phẩm trong đơn hàng.");
                    return ack;
                }

                // Get all vendor information in one query
                var vendorIds = vendorGroups.Keys.ToList();
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                    filter: v => vendorIds.Contains(v.ID)
                );
                var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);

                var createdChildPAs = new List<PurchaseAgreementViewModel>();

                // Generate unique GroupCode using dedicated generator
                var groupCode = await Generator.GenerateEntityCodeAsync(EntityPrefix.PAGroupCode, DbContext);

                // Prepare all Purchase Agreements and Items for batch insert
                var purchaseAgreements = new List<Purchase_Agreement>();
                var allAgreementItems = new List<Purchase_Agreement_Item>();
                var currentDateTime = DateTime.Now;

                // Create purchase agreement for each vendor (Child PAs)
                foreach (var vendorGroup in vendorGroups)
                {
                    var vendorId = vendorGroup.Key;
                    var items = vendorGroup.Value;

                    // Group items by target product and sum quantities
                    var productGroups = items.GroupBy(i => i.TargetProductId)
                        .Select(g => new
                        {
                            ProductId = g.Key,
                            TotalQuantity = g.Sum(i => i.Quantity),
                            Price = g.First().Price,
                            UnitId = g.First().Unit_ID,
                            POItemIds = string.Join(",", g.Select(i => i.POItemId))
                        }).ToList();

                    // Create purchase agreement (Child PA)
                    var purchaseAgreement = new Purchase_Agreement
                    {
                        Vendor_ID = vendorId,
                        Code = await Generator.GenerateEntityCodeAsync(EntityPrefix.PurchaseAgreement, DbContext),
                        GroupCode = groupCode,
                        TotalPrice = productGroups.Sum(pg =>
                        {
                            var vendorPrice = productVendorPriceLookup.TryGetValue($"{pg.ProductId}_{vendorId}", out var price) ? price : (pg.Price ?? 0);
                            return vendorPrice * pg.TotalQuantity;
                        }),
                        Status = EPAStatus.New.ToString(),
                        CreatedDate = currentDateTime,
                        CreatedBy = CurrentUserId,
                        UpdatedDate = currentDateTime,
                        UpdatedBy = CurrentUserId,
                        IsActive = true
                    };

                    purchaseAgreements.Add(purchaseAgreement);

                    // Prepare agreement items with vendor prices
                    var agreementItems = productGroups.Select(productGroup =>
                    {
                        // Use vendor price if available, fallback to PO item price
                        var vendorPrice = productVendorPriceLookup.TryGetValue($"{productGroup.ProductId}_{vendorId}", out var price) ? price : (productGroup.Price ?? 0);

                        return new Purchase_Agreement_Item
                        {
                            // PA_ID will be set after saving the agreement
                            Product_ID = productGroup.ProductId,
                            Quantity = productGroup.TotalQuantity,
                            Unit_ID = productGroup.UnitId,
                            Price = vendorPrice, // Use vendor price
                            PO_Item_ID_List = productGroup.POItemIds
                        };
                    }).ToList();

                    allAgreementItems.AddRange(agreementItems);
                }

                // Bulk insert all Purchase Agreements using repository without auto-save
                await _purchaseAgreementRepository.Repository.AddRangeWithoutSaveAsync(purchaseAgreements);
                await _purchaseAgreementRepository.Repository.SaveChangesAsync();

                // Set PA_ID for agreement items using a more efficient approach
                var itemIndex = 0;
                foreach (var agreement in purchaseAgreements)
                {
                    var vendorItems = vendorGroups[agreement.Vendor_ID];
                    var productGroupCount = vendorItems.GroupBy(i => i.TargetProductId).Count();

                    for (int i = 0; i < productGroupCount; i++)
                    {
                        allAgreementItems[itemIndex + i].PA_ID = agreement.ID;
                    }
                    itemIndex += productGroupCount;
                }

                // Bulk insert all agreement items using repository without auto-save
                if (allAgreementItems.Count > 0)
                {
                    await _purchaseAgreementItemRepository.Repository.AddRangeWithoutSaveAsync(allAgreementItems);
                    await _purchaseAgreementItemRepository.Repository.SaveChangesAsync();
                }

                // Bulk update purchase orders status to Executed using repository without auto-save
                foreach (var order in confirmedOrders)
                {
                    order.Status = EPOStatus.Executed.ToString();
                    order.UpdatedDate = currentDateTime;
                    order.UpdatedBy = CurrentUserId;
                }

                _purchaseOrderRepository.Repository.UpdateRangeWithoutSave(confirmedOrders);
                await _purchaseOrderRepository.Repository.SaveChangesAsync();

                // Create view models using pre-loaded vendor data
                foreach (var agreement in purchaseAgreements)
                {
                    var agreementViewModel = _mapper.Map<PurchaseAgreementViewModel>(agreement);

                    // Use pre-loaded vendor data
                    if (vendorLookup.TryGetValue(agreement.Vendor_ID, out var vendor))
                    {
                        agreementViewModel.VendorName = vendor.Name;
                    }

                    createdChildPAs.Add(agreementViewModel);
                }

                // Create Parent PA ViewModel
                var paViewModel = new PAGroupViewModel
                {
                    GroupCode = groupCode,
                    TotalPrice = createdChildPAs.Sum(pa => pa.TotalPrice),
                    Status = EPAStatus.New,
                    CreatedDate = DateTime.Now,
                    CreatedBy = CurrentUserId,
                    UpdatedDate = DateTime.Now,
                    UpdatedBy = CurrentUserId,
                    ChildPAs = createdChildPAs
                };

                ack.Data = paViewModel;
                ack.IsSuccess = true;
                ack.AddMessage($"Đã tạo thành công PA tổng hợp với {createdChildPAs.Count} PA con từ {confirmedOrders.Count} đơn hàng.");

                // Commit transaction if everything succeeded
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                // Rollback transaction on any error
                await transaction.RollbackAsync();

                ack.AddMessage($"Lỗi khi tạo PA tổng hợp từ đơn hàng: {ex.Message}");
                _logger.LogError(ex, "Error creating PA from confirmed orders - Transaction rolled back");
            }
            return ack;
        }

        public async Task<Acknowledgement<EditablePAGroupPreviewViewModel>> GetEditablePAGroupPreview()
        {
            var ack = new Acknowledgement<EditablePAGroupPreviewViewModel>();
            try
            {
                // Get all confirmed purchase orders with items in one query
                var confirmedOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: po => po.Status == EPOStatus.Confirmed.ToString() && po.IsActive,
                    includeProperties: "PurchaseOrderItems"
                );

                if (!confirmedOrders.Any())
                {
                    ack.AddMessage("Không có đơn hàng nào ở trạng thái Đã xác nhận để tạo PA tổng hợp.");
                    return ack;
                }

                // Use included items instead of separate query
                var allOrderItems = confirmedOrders.SelectMany(po => po.PurchaseOrderItems).ToList();

                if (!allOrderItems.Any())
                {
                    ack.AddMessage("Không có sản phẩm nào trong các đơn hàng đã xác nhận.");
                    return ack;
                }

                // Get product information for all products and their parents in one query
                var productIds = allOrderItems.Select(poi => poi.Product_ID).Distinct().ToList();
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    filter: p => productIds.Contains(p.ID)
                );

                // Get parent product IDs and fetch all products (original + parents) in one query
                var parentProductIds = products.Where(p => p.ParentID.HasValue)
                                              .Select(p => p.ParentID!.Value)
                                              .Distinct()
                                              .Except(productIds) // Only get parents not already loaded
                                              .ToList();

                if (parentProductIds.Any())
                {
                    var parentProducts = await _productRepository.ReadOnlyRespository.GetAsync(
                        filter: p => parentProductIds.Contains(p.ID)
                    );
                    products = products.Concat(parentProducts).ToList();
                }

                var productLookup = products.ToDictionary(p => p.ID, p => p);

                // Get unit information for all units
                var unitIds = allOrderItems.Where(poi => poi.Unit_ID.HasValue).Select(poi => poi.Unit_ID.Value).Distinct().ToList();
                var units = await _unitRepository.ReadOnlyRespository.GetAsync(
                    filter: u => unitIds.Contains(u.ID)
                );
                var unitLookup = units.ToDictionary(u => u.ID, u => u);

                // OPTIMIZED: Get cached parsed vendors
                var parsedVendors = await GetCachedParsedVendors();

                // Transform child products to parent products and calculate quantities with loss rate
                var transformedOrderItems = allOrderItems.Select(item =>
                {
                    var product = productLookup.TryGetValue(item.Product_ID, out var prod) ? prod : null;

                    // If product has a parent, use parent product instead
                    var targetProductId = product?.ParentID ?? item.Product_ID;
                    var targetProduct = productLookup.TryGetValue(targetProductId, out var targetProd) ? targetProd : product;

                    // Calculate quantity with loss rate for all products that have LossRate
                    var adjustedQuantity = item.Quantity;
                    if (product?.LossRate.HasValue == true)
                    {
                        // Apply loss rate: (100 + LossRate) * Quantity / 100
                        adjustedQuantity = (100 + product.LossRate.Value) * item.Quantity / 100;
                    }

                    return new
                    {
                        OriginalProductId = item.Product_ID,
                        TargetProductId = targetProductId,
                        TargetProduct = targetProduct,
                        Quantity = adjustedQuantity,
                        Price = item.Price,
                        UnitId = targetProduct?.Unit_ID ?? item.Unit_ID,
                        POItemId = item.ID
                    };
                }).ToList();

                // Get all target product IDs (including parent products)
                var allTargetProductIds = transformedOrderItems.Select(i => i.TargetProductId).Distinct().ToList();

                // Get product-vendor relationships for all target products in one call
                var allProductVendors = await _productVendorRepository.GetByProductIdsAsync(allTargetProductIds);
                var productVendorLookup = allProductVendors.ToLookup(pv => pv.Product_ID, pv => pv.Vendor_ID);

                // Create default vendor lookup from the same data (highest priority = lowest priority number)
                var defaultVendorLookup = allProductVendors
                    .GroupBy(pv => pv.Product_ID)
                    .ToDictionary(
                        g => g.Key,
                        g => g.OrderBy(pv => pv.Priority ?? int.MaxValue)
                              .ThenBy(pv => pv.UnitPrice ?? decimal.MaxValue)
                              .First().Vendor_ID
                    );

                // Create product-vendor price lookup for getting vendor prices
                var productVendorPriceLookup = allProductVendors
                    .ToDictionary(pv => $"{pv.Product_ID}_{pv.Vendor_ID}", pv => pv.UnitPrice ?? 0);

                // Group items by target product (parent product) and create mappings
                var productGroups = transformedOrderItems.GroupBy(i => i.TargetProductId)
                    .Select(g => new
                    {
                        ProductId = g.Key,
                        TotalQuantity = g.Sum(i => i.Quantity),
                        Price = g.First().Price,
                        UnitId = g.First().UnitId,
                        POItemIds = string.Join(",", g.Select(i => i.POItemId))
                    }).ToList();

                var productVendorMappings = new List<ProductVendorMappingViewModel>();

                foreach (var productGroup in productGroups)
                {
                    var product = productLookup.TryGetValue(productGroup.ProductId, out var prod) ? prod : null;
                    var unit = productGroup.UnitId.HasValue && unitLookup.TryGetValue(productGroup.UnitId.Value, out var u) ? u : null;

                    // Get available vendors for this product from product-vendor relationships
                    var availableVendorIds = productVendorLookup.Contains(productGroup.ProductId)
                        ? productVendorLookup[productGroup.ProductId].ToList()
                        : new List<int>();

                    // Filter pre-parsed vendors to only those available for this product and add prices
                    var availableVendors = parsedVendors
                        .Where(vendor => availableVendorIds.Contains(vendor.ID))
                        .Select(vendor =>
                        {
                            // Get price for this vendor-product combination
                            var vendorPrice = productVendorPriceLookup.TryGetValue($"{productGroup.ProductId}_{vendor.ID}", out var price) ? price : 0;

                            return new VendorOptionViewModel
                            {
                                ID = vendor.ID,
                                Name = vendor.Name,
                                Code = vendor.Code,
                                Price = vendorPrice // Add price to vendor option
                            };
                        })
                        .ToList();

                    // Use highest priority vendor as default, fallback to first available vendor
                    var defaultVendorId = defaultVendorLookup.TryGetValue(productGroup.ProductId, out var highestPriorityVendorId)
                        ? highestPriorityVendorId
                        : availableVendorIds.FirstOrDefault();
                    var defaultVendor = availableVendors.FirstOrDefault(v => v.ID == defaultVendorId);

                    // Get vendor price from product-vendor relationship
                    var vendorPrice = defaultVendor != null
                        ? productVendorPriceLookup.TryGetValue($"{productGroup.ProductId}_{defaultVendor.ID}", out var price) ? price : 0
                        : 0;

                    var mapping = new ProductVendorMappingViewModel
                    {
                        Product_ID = productGroup.ProductId,
                        ProductName = product?.Name,
                        ProductCode = product?.Code,
                        TotalQuantity = productGroup.TotalQuantity,
                        Unit_ID = productGroup.UnitId,
                        UnitName = unit?.Name,
                        Price = vendorPrice, // Use vendor price instead of PO item price
                        Vendor_ID = defaultVendor?.ID ?? 0,
                        VendorName = defaultVendor?.Name,
                        AvailableVendors = availableVendors,
                        PO_Item_ID_List = productGroup.POItemIds
                    };

                    productVendorMappings.Add(mapping);
                }

                // Create editable preview
                var editablePreview = new EditablePAGroupPreviewViewModel
                {
                    GroupCode = "[Preview] Sẽ được tạo tự động",
                    TotalPrice = productVendorMappings.Sum(m => m.TotalAmount),
                    Status = EPAStatus.New,
                    ProductVendorMappings = productVendorMappings
                };

                ack.Data = editablePreview;
                ack.IsSuccess = true;
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi lấy preview PA tổng hợp có thể chỉnh sửa: {ex.Message}");
                _logger.LogError(ex, "Error getting editable PA group preview");
            }
            return ack;
        }

        public async Task<Acknowledgement<PAGroupViewModel>> CreatePAGroupWithCustomMapping(CreatePAGroupWithMappingRequest request)
        {
            var ack = new Acknowledgement<PAGroupViewModel>();

            if (request?.ProductVendorMappings == null || !request.ProductVendorMappings.Any())
            {
                ack.AddMessage("Dữ liệu mapping sản phẩm-nhà cung cấp không hợp lệ.");
                return ack;
            }

            // Use database transaction to ensure data consistency
            using var transaction = await DbContext.Database.BeginTransactionAsync();
            try
            {
                // Validate that all products have valid vendor assignments
                var invalidMappings = request.ProductVendorMappings.Where(m => m.Vendor_ID <= 0).ToList();
                if (invalidMappings.Any())
                {
                    ack.AddMessage($"Có {invalidMappings.Count} sản phẩm chưa được gán nhà cung cấp hợp lệ.");
                    return ack;
                }

                // Get all PO Item IDs from mappings
                var allPOItemIds = new List<int>();
                foreach (var mapping in request.ProductVendorMappings)
                {
                    if (!string.IsNullOrEmpty(mapping.PO_Item_ID_List))
                    {
                        var poItemIds = mapping.PO_Item_ID_List.Split(',')
                            .Where(id => int.TryParse(id.Trim(), out _))
                            .Select(id => int.Parse(id.Trim()))
                            .ToList();
                        allPOItemIds.AddRange(poItemIds);
                    }
                }

                // Get related Purchase Orders and update their status to Executed
                var relatedPOIds = new List<int>();
                if (allPOItemIds.Any())
                {
                    var poItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                        filter: poi => allPOItemIds.Contains(poi.ID)
                    );
                    relatedPOIds = poItems.Select(poi => poi.PO_ID).Distinct().ToList();
                }

                var relatedPOs = new List<Purchase_Order>();
                if (relatedPOIds.Any())
                {
                    relatedPOs = (await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                        filter: po => relatedPOIds.Contains(po.ID) && po.Status == EPOStatus.Confirmed.ToString() && po.IsActive
                    )).ToList();

                    // Update PO status to Executed
                    foreach (var po in relatedPOs)
                    {
                        po.Status = EPOStatus.Executed.ToString();
                        po.UpdatedDate = DateTime.Now;
                        po.UpdatedBy = CurrentUserId;
                    }

                    await _purchaseOrderRepository.Repository.UpdateRangeAsync(relatedPOs);
                    await _purchaseOrderRepository.Repository.SaveChangesAsync();
                }

                // Generate unique GroupCode
                var groupCode = await Generator.GenerateEntityCodeAsync(EntityPrefix.PAGroupCode, DbContext);

                // Group mappings by vendor
                var vendorGroups = request.ProductVendorMappings.GroupBy(m => m.Vendor_ID).ToList();

                // Get vendor information
                var vendorIds = vendorGroups.Select(g => g.Key).ToList();
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                    filter: v => vendorIds.Contains(v.ID)
                );
                var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);

                // Get product-vendor relationships for price lookup
                var allProductIds = request.ProductVendorMappings.Select(m => m.Product_ID).Distinct().ToList();
                var allProductVendors = await _productVendorRepository.GetByProductIdsAsync(allProductIds);
                var productVendorPriceLookup = allProductVendors
                    .ToDictionary(pv => $"{pv.Product_ID}_{pv.Vendor_ID}", pv => pv.UnitPrice ?? 0);

                var createdChildPAs = new List<PurchaseAgreementViewModel>();
                var purchaseAgreements = new List<Purchase_Agreement>();
                var allAgreementItems = new List<Purchase_Agreement_Item>();
                var currentDateTime = DateTime.Now;

                // Create purchase agreement for each vendor
                foreach (var vendorGroup in vendorGroups)
                {
                    var vendorId = vendorGroup.Key;
                    var mappings = vendorGroup.ToList();

                    // Calculate total price using vendor prices
                    var totalPrice = mappings.Sum(m =>
                    {
                        var vendorPrice = productVendorPriceLookup.TryGetValue($"{m.Product_ID}_{m.Vendor_ID}", out var price) ? price : (m.Price ?? 0);
                        return (decimal)(m.TotalQuantity * vendorPrice);
                    });

                    // Create purchase agreement
                    var purchaseAgreement = new Purchase_Agreement
                    {
                        Vendor_ID = vendorId,
                        Code = await Generator.GenerateEntityCodeAsync(EntityPrefix.PurchaseAgreement, DbContext),
                        GroupCode = groupCode,
                        TotalPrice = totalPrice, // Use calculated total price with vendor prices
                        Status = EPAStatus.New.ToString(),
                        CreatedDate = currentDateTime,
                        CreatedBy = CurrentUserId,
                        UpdatedDate = currentDateTime,
                        UpdatedBy = CurrentUserId,
                        IsActive = true
                    };

                    purchaseAgreements.Add(purchaseAgreement);

                    // Prepare agreement items with vendor prices
                    var agreementItems = mappings.Select(mapping =>
                    {
                        // Use vendor price if available, fallback to mapping price
                        var vendorPrice = productVendorPriceLookup.TryGetValue($"{mapping.Product_ID}_{mapping.Vendor_ID}", out var price) ? price : mapping.Price;

                        return new Purchase_Agreement_Item
                        {
                            Product_ID = mapping.Product_ID,
                            Quantity = mapping.TotalQuantity,
                            Unit_ID = mapping.Unit_ID,
                            Price = vendorPrice, // Use vendor price
                            PO_Item_ID_List = mapping.PO_Item_ID_List
                        };
                    }).ToList();

                    allAgreementItems.AddRange(agreementItems);
                }

                // Save purchase agreements
                await _purchaseAgreementRepository.Repository.AddRangeAsync(purchaseAgreements);
                await _purchaseAgreementRepository.Repository.SaveChangesAsync();

                // Set PA_ID for agreement items and save them
                var itemIndex = 0;
                foreach (var pa in purchaseAgreements)
                {
                    var vendorMappings = request.ProductVendorMappings.Where(m => m.Vendor_ID == pa.Vendor_ID).ToList();
                    for (int i = 0; i < vendorMappings.Count; i++)
                    {
                        allAgreementItems[itemIndex].PA_ID = pa.ID;
                        itemIndex++;
                    }
                }

                await _purchaseAgreementItemRepository.Repository.AddRangeAsync(allAgreementItems);
                await _purchaseAgreementItemRepository.Repository.SaveChangesAsync();

                // Create response ViewModels
                foreach (var pa in purchaseAgreements)
                {
                    var vendor = vendorLookup.TryGetValue(pa.Vendor_ID, out var v) ? v : null;
                    var vendorMappings = request.ProductVendorMappings.Where(m => m.Vendor_ID == pa.Vendor_ID).ToList();

                    var paItems = vendorMappings.Select(mapping =>
                    {
                        // Use vendor price if available, fallback to mapping price
                        var vendorPrice = productVendorPriceLookup.TryGetValue($"{mapping.Product_ID}_{mapping.Vendor_ID}", out var price) ? price : (mapping.Price ?? 0);

                        return new PurchaseAgreementItemViewModel
                        {
                            Product_ID = mapping.Product_ID,
                            ProductName = mapping.ProductName,
                            Quantity = mapping.TotalQuantity,
                            Unit_ID = mapping.Unit_ID,
                            UnitName = mapping.UnitName,
                            Price = vendorPrice // Use vendor price
                        };
                    }).ToList();

                    var paViewModel = new PurchaseAgreementViewModel
                    {
                        Id = pa.ID,
                        Code = pa.Code,
                        Vendor_ID = pa.Vendor_ID,
                        VendorName = vendor?.Name,
                        GroupCode = pa.GroupCode,
                        TotalPrice = pa.TotalPrice,
                        Status = EPAStatus.New,
                        PurchaseAgreementItems = paItems
                    };

                    createdChildPAs.Add(paViewModel);
                }

                // Create Parent PA ViewModel
                var paGroupViewModel = new PAGroupViewModel
                {
                    GroupCode = groupCode,
                    TotalPrice = createdChildPAs.Sum(pa => pa.TotalPrice),
                    Status = EPAStatus.New,
                    CreatedDate = DateTime.Now,
                    CreatedBy = CurrentUserId,
                    UpdatedDate = DateTime.Now,
                    UpdatedBy = CurrentUserId,
                    ChildPAs = createdChildPAs
                };

                ack.Data = paGroupViewModel;
                ack.IsSuccess = true;
                ack.AddMessage($"Đã tạo thành công PA tổng hợp với {createdChildPAs.Count} PA con từ {relatedPOs.Count} đơn hàng.");

                // Commit transaction if everything succeeded
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                // Rollback transaction on any error
                await transaction.RollbackAsync();

                ack.AddMessage($"Lỗi khi tạo PA tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error creating PA group with custom mapping - Transaction rolled back");
            }
            return ack;
        }

        public async Task<Acknowledgement> SendToVendor(string groupCode)
        {
            var ack = new Acknowledgement();

            // Use database transaction to ensure data consistency
            using var transaction = await DbContext.Database.BeginTransactionAsync();
            try
            {
                if (string.IsNullOrWhiteSpace(groupCode))
                {
                    ack.AddMessage("Mã nhóm không được để trống.");
                    return ack;
                }

                // Get all purchase agreements in this group
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive
                );

                if (!purchaseAgreements.Any())
                {
                    ack.AddMessage("Không tìm thấy PA tổng hợp với mã nhóm này.");
                    return ack;
                }

                // Check if user has permission to update these PAs using author predicate
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.GroupCode == groupCode && p.IsActive);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(filter: predicate);
                if (!authorizedPurchaseAgreements.Any())
                {
                    ack.AddMessage("Bạn không có quyền cập nhật PA tổng hợp này.");
                    return ack;
                }

                // Check if all PAs are in New status (can only send from New status)
                var invalidStatusPAs = purchaseAgreements.Where(pa => pa.Status != EPAStatus.New.ToString()).ToList();
                if (invalidStatusPAs.Any())
                {
                    ack.AddMessage($"Chỉ có thể gửi PA tổng hợp ở trạng thái 'Mới'. Có {invalidStatusPAs.Count} PA không ở trạng thái phù hợp.");
                    return ack;
                }

                // Send emails to vendors first
                var emailResult = await SendEmailsToVendors(groupCode);

                // Update PA statuses based on email results using optimized helper method
                await UpdatePAStatusesBasedOnEmailResults(purchaseAgreements, emailResult);

                // Commit transaction if everything succeeded
                await transaction.CommitAsync();

                _logger.LogInformation($"Updated individual PA statuses for group {groupCode}. Emails sent: {emailResult.emailsSent}, Errors: {emailResult.emailErrors.Count}");

                // Mark operation as failed only if ALL emails failed
                // Partial success is still considered success with warnings
                var allEmailsFailed = emailResult.emailErrors.Any() && emailResult.emailsSent == 0;
                if (allEmailsFailed)
                {
                    ack.IsSuccess = false;
                }
                else
                {
                    ack.IsSuccess = true;
                }

                // Process email results using common method
                ProcessEmailResults(ack, emailResult, "gửi", groupCode);

                // Add status update message for individual PAs
                if (emailResult.emailsSent > 0)
                {
                    var successCount = emailResult.emailsSent;
                    var failureCount = emailResult.emailErrors.Count;

                    if (failureCount == 0)
                    {
                        ack.AddSuccessMessages(new[] { $"Đã gửi email thành công cho tất cả {successCount} PA. Trạng thái đã được cập nhật thành 'Đã gửi NCC'." });
                    }
                    else
                    {
                        ack.AddSuccessMessages(new[] { $"Đã gửi email cho {successCount} PA thành công, {failureCount} PA thất bại. Trạng thái từng PA đã được cập nhật riêng biệt." });
                    }
                }
            }
            catch (Exception ex)
            {
                // Rollback transaction on any error
                await transaction.RollbackAsync();

                ack.AddMessage($"Lỗi khi gửi PA tổng hợp cho nhà cung cấp: {ex.Message}");
                _logger.LogError(ex, "Error sending PA group to vendor - Transaction rolled back");
            }
            return ack;
        }

        public async Task<Acknowledgement> CompletePAGroup(string groupCode)
        {
            var ack = new Acknowledgement();
            try
            {
                if (string.IsNullOrWhiteSpace(groupCode))
                {
                    ack.AddMessage("Mã nhóm không được để trống.");
                    return ack;
                }

                // Get all purchase agreements in this group
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive
                );

                if (!purchaseAgreements.Any())
                {
                    ack.AddMessage("Không tìm thấy PA tổng hợp với mã nhóm này.");
                    return ack;
                }

                // Check if user has permission to update these PAs using author predicate
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.GroupCode == groupCode && p.IsActive);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(filter: predicate);
                if (!authorizedPurchaseAgreements.Any())
                {
                    ack.AddMessage("Bạn không có quyền cập nhật PA tổng hợp này.");
                    return ack;
                }

                // Check if all PAs are in SendVendor status (can only complete from SendVendor status)
                var invalidStatusPAs = purchaseAgreements.Where(pa => pa.Status != EPAStatus.SendVendor.ToString()).ToList();
                if (invalidStatusPAs.Any())
                {
                    ack.AddMessage($"Chỉ có thể hoàn thành PA tổng hợp ở trạng thái 'Đã gửi NCC'. Có {invalidStatusPAs.Count} PA không ở trạng thái phù hợp.");
                    return ack;
                }

                // Update all PAs in the group to Completed status
                foreach (var pa in purchaseAgreements)
                {
                    pa.Status = EPAStatus.Completed.ToString();
                    pa.UpdatedDate = DateTime.Now;
                    pa.UpdatedBy = CurrentUserId;
                }

                // Save changes
                await ack.TrySaveChangesAsync(res => res.UpdateRangeAsync(purchaseAgreements), _purchaseAgreementRepository.Repository);

                if (ack.IsSuccess)
                {
                    ack.AddMessage($"Đã hoàn thành PA tổng hợp '{groupCode}' thành công.");
                }
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi hoàn thành PA tổng hợp: {ex.Message}");
                _logger.LogError(ex, "Error completing PA group");
            }
            return ack;
        }

        /// <summary>
        /// Cancel a PA Group and revert related Purchase Orders back to Confirmed status
        /// </summary>
        /// <param name="groupCode">Group code of the PA group to cancel</param>
        /// <returns>Result of the cancellation operation</returns>
        public async Task<Acknowledgement> CancelPAGroup(string groupCode)
        {
            var ack = new Acknowledgement();

            // Use database transaction to ensure data consistency
            using var transaction = await DbContext.Database.BeginTransactionAsync();
            try
            {
                if (string.IsNullOrEmpty(groupCode))
                {
                    ack.AddMessage("Mã nhóm PA không hợp lệ.");
                    return ack;
                }

                // Get all PAs in the group
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive
                );

                if (!purchaseAgreements.Any())
                {
                    ack.AddMessage("Không tìm thấy PA nào trong nhóm này.");
                    return ack;
                }

                // Check authorization for all PAs in the group
                var predicate = PredicateBuilder.New<Purchase_Agreement>(true);
                predicate = predicate.And(p => p.GroupCode == groupCode && p.IsActive);
                predicate = PurchaseAgreementAuthorPredicate.GetPurchaseAgreementAuthorPredicate(predicate, CurrentUserRoles, CurrentUserId);

                var authorizedPurchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(filter: predicate);
                if (!authorizedPurchaseAgreements.Any())
                {
                    ack.AddMessage("Bạn không có quyền hủy PA tổng hợp này.");
                    await transaction.RollbackAsync();
                    return ack;
                }

                // Check if PAs can be cancelled - only allow cancellation for specific statuses
                var allowedCancelStatuses = new[] {
                    EPAStatus.New.ToString(),
                    EPAStatus.SendVendor.ToString(),
                    EPAStatus.EmailFailed.ToString(),
                    EPAStatus.PartialEmailSent.ToString()
                };

                var invalidStatusPAs = purchaseAgreements.Where(pa => !allowedCancelStatuses.Contains(pa.Status)).ToList();
                if (invalidStatusPAs.Any())
                {
                    var invalidStatuses = string.Join(", ", invalidStatusPAs.Select(pa => $"'{EnumHelper.GetEnumDescriptionByEnum(Enum.Parse<EPAStatus>(pa.Status))}'"));
                    ack.AddMessage($"Không thể hủy PA tổng hợp. Có {invalidStatusPAs.Count} PA ở trạng thái không cho phép hủy: {invalidStatuses}. Chỉ có thể hủy PA ở trạng thái: Mới, Đã gửi NCC, Gửi email thất bại, Gửi email một phần.");
                    await transaction.RollbackAsync();
                    return ack;
                }

                // Get all PA Items to find related Purchase Orders
                var paItemIds = purchaseAgreements.Select(pa => pa.ID).ToList();
                var paItems = await _purchaseAgreementItemRepository.ReadOnlyRespository.GetAsync(
                    filter: pai => paItemIds.Contains(pai.PA_ID)
                );

                // Extract PO Item IDs from PA Items
                var allPOItemIds = new List<int>();
                foreach (var paItem in paItems)
                {
                    if (!string.IsNullOrEmpty(paItem.PO_Item_ID_List))
                    {
                        var poItemIds = paItem.PO_Item_ID_List.Split(',')
                            .Where(id => int.TryParse(id.Trim(), out _))
                            .Select(id => int.Parse(id.Trim()))
                            .ToList();
                        allPOItemIds.AddRange(poItemIds);
                    }
                }

                // Get related Purchase Orders and revert their status
                var relatedPOIds = new List<int>();
                if (allPOItemIds.Any())
                {
                    var poItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                        filter: poi => allPOItemIds.Contains(poi.ID)
                    );
                    relatedPOIds = poItems.Select(poi => poi.PO_ID).Distinct().ToList();
                }

                var relatedPOs = new List<Purchase_Order>();
                if (relatedPOIds.Any())
                {
                    relatedPOs = (await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                        filter: po => relatedPOIds.Contains(po.ID) && po.Status == EPOStatus.Executed.ToString() && po.IsActive
                    )).ToList();

                    // Revert PO status from Executed back to Confirmed
                    foreach (var po in relatedPOs)
                    {
                        po.Status = EPOStatus.Confirmed.ToString();
                        po.UpdatedDate = DateTime.Now;
                        po.UpdatedBy = CurrentUserId;
                    }

                    await _purchaseOrderRepository.Repository.UpdateRangeAsync(relatedPOs);
                    await _purchaseOrderRepository.Repository.SaveChangesAsync();
                }

                // Update all PAs in the group to Cancel status
                foreach (var pa in purchaseAgreements)
                {
                    pa.Status = EPAStatus.Cancel.ToString();
                    pa.UpdatedDate = DateTime.Now;
                    pa.UpdatedBy = CurrentUserId;
                }

                await _purchaseAgreementRepository.Repository.UpdateRangeAsync(purchaseAgreements);
                await _purchaseAgreementRepository.Repository.SaveChangesAsync();

                // Commit transaction
                await transaction.CommitAsync();

                ack.IsSuccess = true;
                ack.AddMessage($"Hủy PA tổng hợp thành công. Đã hủy {purchaseAgreements.Count()} PA và khôi phục {relatedPOs.Count} đơn hàng PO về trạng thái 'Đã xác nhận'.");

                _logger.LogInformation($"Cancelled PA Group {groupCode}. Updated {purchaseAgreements.Count()} PAs and reverted {relatedPOs.Count} POs to Confirmed status.");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError("CancelPAGroup failed: {ErrorMessage}", ex.Message);
                ack.AddMessage($"Lỗi khi hủy PA tổng hợp: {ex.Message}");
            }

            return ack;
        }

        private async Task SavePurchaseAgreementItems(int purchaseAgreementId, List<PurchaseAgreementItemViewModel>? items)
        {
            if (items != null)
            {
                foreach (var item in items)
                {
                    var agreementItem = _mapper.Map<Purchase_Agreement_Item>(item);
                    agreementItem.PA_ID = purchaseAgreementId;
                    await _purchaseAgreementItemRepository.Repository.AddAsync(agreementItem);
                }
                await DbContext.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Generate email content for individual PA
        /// </summary>
        private async Task<string> GeneratePAEmailContent(PurchaseAgreementViewModel pa, Vendor vendor)
        {
            try
            {
                // Get template path
                var templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "templates", "pa_email_template.html");

                if (!File.Exists(templatePath))
                {
                    _logger.LogWarning($"PA email template not found at {templatePath}");
                    return await GenerateFallbackPAEmailContent(pa, vendor);
                }

                // Read template
                var template = await File.ReadAllTextAsync(templatePath);

                // Get product information for email items
                var productIds = pa.PurchaseAgreementItems?.Select(i => i.Product_ID).Distinct().ToList() ?? new List<int>();
                var products = new Dictionary<int, Product>();
                if (productIds.Any())
                {
                    var productList = await _productRepository.ReadOnlyRespository.GetAsync(filter: p => productIds.Contains(p.ID));
                    products = productList.ToDictionary(p => p.ID, p => p);
                }

                // Create email data with null-safe handling
                var emailData = new PAEmailViewModel
                {
                    PACode = pa.Code ?? "",
                    GroupCode = pa.GroupCode ?? "",
                    CreatedDate = pa.CreatedDate.ToString("dd/MM/yyyy HH:mm"),
                    Note = pa.Note ?? "",
                    VendorName = vendor.Name ?? "",
                    VendorCode = vendor.Code ?? "",
                    VendorTaxCode = vendor.TaxCode ?? "",
                    VendorAddress = vendor.Address ?? "",
                    VendorPhoneNumber = vendor.PhoneNumber ?? "",
                    VendorEmail = vendor.Email ?? "",
                    TotalPrice = pa.TotalPrice.ToString("N0"),
                    SendDate = DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
                    PAItems = pa.PurchaseAgreementItems?.Select((item, index) => new PAEmailItemViewModel
                    {
                        Index = index + 1,
                        ProductCode = products.TryGetValue(item.Product_ID, out var product) ? (product.Code ?? "") : "",
                        ProductName = item.ProductName ?? "",
                        Quantity = item.Quantity.ToString("N2"),
                        UnitName = item.UnitName ?? "",
                        Price = (item.Price ?? 0).ToString("N0"),
                        LineTotal = ((item.Price ?? 0) * item.Quantity).ToString("N0")
                    }).ToList() ?? new List<PAEmailItemViewModel>()
                };

                // Process template with data
                var html = await ProcessPAEmailTemplate(template, emailData);
                return html;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PA email content");
                return await GenerateFallbackPAEmailContent(pa, vendor);
            }
        }

        /// <summary>
        /// Process PA email template with data
        /// </summary>
        private Task<string> ProcessPAEmailTemplate(string template, PAEmailViewModel data)
        {
            try
            {
                var html = template;

                // Replace simple variables with null-safe handling
                html = html.Replace("{{PACode}}", data.PACode ?? "");
                html = html.Replace("{{GroupCode}}", data.GroupCode ?? "");
                html = html.Replace("{{CreatedDate}}", data.CreatedDate ?? "");
                html = html.Replace("{{VendorName}}", data.VendorName ?? "");
                html = html.Replace("{{VendorCode}}", data.VendorCode ?? "");
                html = html.Replace("{{VendorTaxCode}}", data.VendorTaxCode ?? "");
                html = html.Replace("{{VendorAddress}}", data.VendorAddress ?? "");
                html = html.Replace("{{VendorPhoneNumber}}", data.VendorPhoneNumber ?? "");
                html = html.Replace("{{VendorEmail}}", data.VendorEmail ?? "");
                html = html.Replace("{{TotalPrice}}", data.TotalPrice ?? "");
                html = html.Replace("{{SendDate}}", data.SendDate ?? "");
                html = html.Replace("{{Note}}", data.Note ?? "");

                // Handle conditional blocks
                html = ProcessConditionalBlock(html, "Note", data.Note);
                html = ProcessConditionalBlock(html, "VendorTaxCode", data.VendorTaxCode);
                html = ProcessConditionalBlock(html, "VendorAddress", data.VendorAddress);
                html = ProcessConditionalBlock(html, "VendorPhoneNumber", data.VendorPhoneNumber);
                html = ProcessConditionalBlock(html, "VendorEmail", data.VendorEmail);

                // Process PA Items loop
                html = ProcessPAItemsLoop(html, data.PAItems);

                // AGGRESSIVE CLEANUP: Remove ALL possible placeholder patterns
                html = AggressiveCleanupPlaceholders(html);

                return Task.FromResult(html);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing PA email template");
                return Task.FromResult(template); // Return original template if processing fails
            }
        }

        /// <summary>
        /// Process conditional blocks in template - handles multiple occurrences
        /// </summary>
        private string ProcessConditionalBlock(string html, string fieldName, string? value)
        {
            var startTag = $"{{#{fieldName}}}";
            var endTag = $"{{/{fieldName}}}";

            // Process all occurrences of this conditional block
            while (true)
            {
                var startIndex = html.IndexOf(startTag);
                if (startIndex == -1) break;

                var endIndex = html.IndexOf(endTag, startIndex);
                if (endIndex == -1) break;

                var blockContent = html.Substring(startIndex + startTag.Length, endIndex - startIndex - startTag.Length);

                if (!string.IsNullOrWhiteSpace(value))
                {
                    // Replace the field placeholder in the block content
                    var processedContent = blockContent.Replace($"{{{{{fieldName}}}}}", value);
                    html = html.Substring(0, startIndex) + processedContent + html.Substring(endIndex + endTag.Length);
                }
                else
                {
                    // Remove the entire block if value is empty
                    html = html.Substring(0, startIndex) + html.Substring(endIndex + endTag.Length);
                }
            }

            return html;
        }

        /// <summary>
        /// Clean up all template placeholders while preserving CSS
        /// </summary>
        private string AggressiveCleanupPlaceholders(string html)
        {
            _logger.LogInformation("Starting template placeholder cleanup...");

            // Log initial state
            LogPlaceholders(html, "BEFORE cleanup");

            // Remove all template placeholders
            html = RemoveTemplatePlaceholders(html);
            html = RemoveSingleBracesOutsideCSS(html);
            html = RemoveEmptyLines(html);

            // Log final state
            LogPlaceholders(html, "AFTER cleanup");

            return html;
        }

        /// <summary>
        /// Remove template placeholders (double braces)
        /// </summary>
        private string RemoveTemplatePlaceholders(string html)
        {
            // Remove conditional blocks and simple placeholders
            html = System.Text.RegularExpressions.Regex.Replace(html, @"\{\{#[^}]*\}\}", "");
            html = System.Text.RegularExpressions.Regex.Replace(html, @"\{\{/[^}]*\}\}", "");
            html = System.Text.RegularExpressions.Regex.Replace(html, @"\{\{[^}]*\}\}", "");
            return html;
        }

        /// <summary>
        /// Remove empty lines left after placeholder removal
        /// </summary>
        private string RemoveEmptyLines(string html)
        {
            return System.Text.RegularExpressions.Regex.Replace(html, @"^\s*$\n", "", System.Text.RegularExpressions.RegexOptions.Multiline);
        }

        /// <summary>
        /// Update PA statuses based on email results with optimized vendor lookup
        /// </summary>
        private async Task UpdatePAStatusesBasedOnEmailResults(
            IEnumerable<Purchase_Agreement> purchaseAgreements,
            (int emailsSent, List<string> emailErrors) emailResult)
        {
            // Convert to list for repository method
            var purchaseAgreementsList = purchaseAgreements.ToList();

            // Pre-load all vendors for this PA group to avoid N+1 queries
            var vendorIds = purchaseAgreementsList.Select(pa => pa.Vendor_ID).Distinct().ToList();
            var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                filter: v => vendorIds.Contains(v.ID)
            );
            var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);

            var currentDateTime = DateTime.Now;
            foreach (var pa in purchaseAgreementsList)
            {
                if (!vendorLookup.TryGetValue(pa.Vendor_ID, out var vendor))
                {
                    // Vendor not found - mark as failed
                    pa.Status = EPAStatus.EmailFailed.ToString();
                }
                else if (string.IsNullOrWhiteSpace(vendor.Email))
                {
                    // Vendor has no email - mark as failed
                    pa.Status = EPAStatus.EmailFailed.ToString();
                }
                else
                {
                    // Vendor has email - check if email was sent successfully
                    var emailSentSuccessfully = !emailResult.emailErrors.Any(e => e.Contains($"VENDOR_EMAIL:{vendor.Email}|"));

                    if (emailSentSuccessfully)
                    {
                        // This PA's email was sent successfully
                        pa.Status = EPAStatus.SendVendor.ToString();
                    }
                    else
                    {
                        // This PA's email failed
                        pa.Status = EPAStatus.EmailFailed.ToString();
                    }
                }

                pa.UpdatedDate = currentDateTime;
                pa.UpdatedBy = CurrentUserId;
            }

            // Batch update all PAs at once
            _purchaseAgreementRepository.Repository.UpdateRangeWithoutSave(purchaseAgreementsList);
            await _purchaseAgreementRepository.Repository.SaveChangesAsync();
        }

        /// <summary>
        /// Calculate PA Group status based on individual PA statuses
        /// </summary>
        private EPAStatus CalculatePAGroupStatus(IEnumerable<Purchase_Agreement> purchaseAgreements)
        {
            var statuses = purchaseAgreements.Select(pa => Enum.Parse<EPAStatus>(pa.Status)).ToList();

            if (!statuses.Any())
                return EPAStatus.New;

            var sendVendorCount = statuses.Count(s => s == EPAStatus.SendVendor);
            var emailFailedCount = statuses.Count(s => s == EPAStatus.EmailFailed);
            var totalCount = statuses.Count;

            if (sendVendorCount == totalCount)
            {
                // All PAs sent successfully
                return EPAStatus.SendVendor;
            }
            else if (emailFailedCount == totalCount)
            {
                // All PAs failed
                return EPAStatus.EmailFailed;
            }
            else if (sendVendorCount > 0 && emailFailedCount > 0)
            {
                // Mixed results - some success, some failure
                return EPAStatus.PartialEmailSent;
            }
            else
            {
                // Other statuses (New, Cancel, Completed)
                return statuses.First();
            }
        }

        /// <summary>
        /// Log placeholder counts for debugging
        /// </summary>
        private void LogPlaceholders(string html, string stage)
        {
            var doubleBraces = System.Text.RegularExpressions.Regex.Matches(html, @"\{\{[^}]*\}\}");
            var singleBraces = System.Text.RegularExpressions.Regex.Matches(html, @"(?<!\{)\{(?!\{)[^}]*\}(?!\})");

            if (doubleBraces.Count > 0)
            {
                var list = string.Join(", ", doubleBraces.Cast<System.Text.RegularExpressions.Match>().Select(m => m.Value));
                _logger.LogWarning($"{stage} - Found {doubleBraces.Count} double-brace placeholders: {list}");
            }

            if (singleBraces.Count > 0)
            {
                var list = string.Join(", ", singleBraces.Cast<System.Text.RegularExpressions.Match>().Select(m => m.Value));
                _logger.LogWarning($"{stage} - Found {singleBraces.Count} single-brace placeholders: {list}");
            }

            if (doubleBraces.Count == 0 && singleBraces.Count == 0)
            {
                _logger.LogInformation($"{stage} - No template placeholders found, CSS preserved");
            }
        }

        /// <summary>
        /// Remove single braces outside CSS style blocks
        /// </summary>
        private string RemoveSingleBracesOutsideCSS(string html)
        {
            var stylePattern = @"<style[^>]*>.*?</style>";
            var styleMatches = System.Text.RegularExpressions.Regex.Matches(html, stylePattern,
                System.Text.RegularExpressions.RegexOptions.Singleline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            if (styleMatches.Count == 0)
            {
                // No CSS blocks, safe to remove all single braces
                return System.Text.RegularExpressions.Regex.Replace(html, @"(?<!\{)\{(?!\{)[^}]*\}(?!\})", "");
            }

            // Process HTML parts while preserving CSS blocks
            var result = "";
            var lastIndex = 0;

            foreach (System.Text.RegularExpressions.Match match in styleMatches)
            {
                // Clean text before CSS block
                var beforeCSS = html.Substring(lastIndex, match.Index - lastIndex);
                result += System.Text.RegularExpressions.Regex.Replace(beforeCSS, @"(?<!\{)\{(?!\{)[^}]*\}(?!\})", "");

                // Keep CSS block unchanged
                result += match.Value;
                lastIndex = match.Index + match.Length;
            }

            // Clean remaining text after last CSS block
            if (lastIndex < html.Length)
            {
                var afterCSS = html.Substring(lastIndex);
                result += System.Text.RegularExpressions.Regex.Replace(afterCSS, @"(?<!\{)\{(?!\{)[^}]*\}(?!\})", "");
            }

            return result;
        }

        /// <summary>
        /// Process PA items loop in template
        /// </summary>
        private string ProcessPAItemsLoop(string html, List<PAEmailItemViewModel> items)
        {
            var startTag = "{{#PAItems}}";
            var endTag = "{{/PAItems}}";

            var startIndex = html.IndexOf(startTag);
            if (startIndex == -1) return html;

            var endIndex = html.IndexOf(endTag, startIndex);
            if (endIndex == -1) return html;

            var itemTemplate = html.Substring(startIndex + startTag.Length, endIndex - startIndex - startTag.Length);
            var itemsHtml = "";

            foreach (var item in items)
            {
                var itemHtml = itemTemplate;
                itemHtml = itemHtml.Replace("{{Index}}", item.Index.ToString());
                itemHtml = itemHtml.Replace("{{ProductCode}}", item.ProductCode ?? "");
                itemHtml = itemHtml.Replace("{{ProductName}}", item.ProductName ?? "");
                itemHtml = itemHtml.Replace("{{Quantity}}", item.Quantity ?? "");
                itemHtml = itemHtml.Replace("{{UnitName}}", item.UnitName ?? "");
                itemHtml = itemHtml.Replace("{{Price}}", item.Price ?? "");
                itemHtml = itemHtml.Replace("{{LineTotal}}", item.LineTotal ?? "");

                // Clean up any remaining placeholders in this item
                itemHtml = System.Text.RegularExpressions.Regex.Replace(itemHtml, @"\{\{[^}]+\}\}", "");

                itemsHtml += itemHtml;
            }

            html = html.Substring(0, startIndex) + itemsHtml + html.Substring(endIndex + endTag.Length);
            return html;
        }

        /// <summary>
        /// Generate fallback email content when template is not available
        /// </summary>
        private async Task<string> GenerateFallbackPAEmailContent(PurchaseAgreementViewModel pa, Vendor vendor)
        {
            // Get product information for fallback template
            var productIds = pa.PurchaseAgreementItems?.Select(i => i.Product_ID).Distinct().ToList() ?? new List<int>();
            var products = new Dictionary<int, Product>();
            if (productIds.Any())
            {
                var productList = await _productRepository.ReadOnlyRespository.GetAsync(filter: p => productIds.Contains(p.ID));
                products = productList.ToDictionary(p => p.ID, p => p);
            }

            var itemsHtml = "";
            var index = 1;
            foreach (var item in pa.PurchaseAgreementItems ?? new List<PurchaseAgreementItemViewModel>())
            {
                var productCode = products.TryGetValue(item.Product_ID, out var product) ? product.Code : "";
                var lineTotal = (item.Price ?? 0) * item.Quantity;
                itemsHtml += $@"
                    <tr>
                        <td>{index++}</td>
                        <td>{productCode}</td>
                        <td>{item.ProductName}</td>
                        <td>{item.Quantity:N2}</td>
                        <td>{item.UnitName}</td>
                        <td>{item.Price:N0} VNĐ</td>
                        <td>{lineTotal:N0} VNĐ</td>
                    </tr>";
            }

            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <title>Hợp đồng mua hàng - {pa.Code}</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                        .header {{ text-align: center; border-bottom: 2px solid #096b73; padding-bottom: 20px; margin-bottom: 30px; }}
                        .info-section {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }}
                        .info-box {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
                        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                        th {{ background-color: #096b73; color: white; }}
                        .total {{ background-color: #096b73; color: white; padding: 15px; text-align: center; margin: 20px 0; }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h1>HỢP ĐỒNG MUA HÀNG</h1>
                            <h2>{pa.Code}</h2>
                        </div>

                        <div class='info-section'>
                            <div class='info-box'>
                                <h3>THÔNG TIN HỢP ĐỒNG</h3>
                                <p><strong>Mã hợp đồng:</strong> {pa.Code}</p>
                                <p><strong>Mã nhóm:</strong> {pa.GroupCode}</p>
                                <p><strong>Ngày tạo:</strong> {pa.CreatedDate:dd/MM/yyyy HH:mm}</p>
                            </div>

                            <div class='info-box'>
                                <h3>THÔNG TIN NHÀ CUNG CẤP</h3>
                                <p><strong>Tên:</strong> {vendor.Name}</p>
                                <p><strong>Mã:</strong> {vendor.Code}</p>
                                {(!string.IsNullOrWhiteSpace(vendor.TaxCode) ? $"<p><strong>Mã số thuế:</strong> {vendor.TaxCode}</p>" : "")}
                                {(!string.IsNullOrWhiteSpace(vendor.Address) ? $"<p><strong>Địa chỉ:</strong> {vendor.Address}</p>" : "")}
                                {(!string.IsNullOrWhiteSpace(vendor.PhoneNumber) ? $"<p><strong>Điện thoại:</strong> {vendor.PhoneNumber}</p>" : "")}
                                {(!string.IsNullOrWhiteSpace(vendor.Email) ? $"<p><strong>Email:</strong> {vendor.Email}</p>" : "")}
                            </div>
                        </div>

                        <h3>DANH SÁCH SẢN PHẨM</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th>Mã SP</th>
                                    <th>Tên sản phẩm</th>
                                    <th>Số lượng</th>
                                    <th>Đơn vị</th>
                                    <th>Đơn giá</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                {itemsHtml}
                            </tbody>
                        </table>

                        <div class='total'>
                            <h3>TỔNG TIỀN: {pa.TotalPrice:N0} VNĐ</h3>
                        </div>

                        <div style='text-align: center; margin-top: 40px; color: #666;'>
                            <p><strong>Phần mềm quản lý đơn hàng Tasin</strong></p>
                            <p>Email được gửi tự động từ hệ thống</p>
                            <p>Ngày gửi: {DateTime.Now:dd/MM/yyyy HH:mm}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        /// <summary>
        /// Send emails to vendors for a PA group - OPTIMIZED VERSION
        /// </summary>
        /// <param name="groupCode">PA group code</param>
        /// <returns>Email sending results</returns>
        private async Task<(int emailsSent, List<string> emailErrors)> SendEmailsToVendors(string groupCode)
        {
            var emailsSent = 0;
            var emailErrors = new List<string>();

            try
            {
                // OPTIMIZED: Get PA data directly without using GetPAByGroupCode to avoid duplicate queries
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive,
                    orderBy: q => q.OrderBy(pa => pa.Vendor_ID)
                );

                if (!purchaseAgreements.Any())
                {
                    emailErrors.Add("Không tìm thấy PA tổng hợp để gửi email.");
                    return (emailsSent, emailErrors);
                }

                var childPAs = _mapper.Map<List<PurchaseAgreementViewModel>>(purchaseAgreements);

                // OPTIMIZED: Get all vendor data in one query
                var vendorIds = childPAs.Select(pa => pa.Vendor_ID).Distinct().ToList();
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(filter: v => vendorIds.Contains(v.ID));
                var vendorLookup = vendors.ToDictionary(v => v.ID, v => v);

                // OPTIMIZED: Get all PA items data in batch
                var paIds = childPAs.Select(pa => pa.Id).ToList();
                var allItems = await _purchaseAgreementItemRepository.ReadOnlyRespository.GetAsync(
                    filter: item => paIds.Contains(item.PA_ID),
                    orderBy: q => q.OrderBy(i => i.PA_ID).ThenBy(i => i.Product_ID)
                );
                var itemsByPAId = allItems.GroupBy(item => item.PA_ID).ToDictionary(g => g.Key, g => g.ToList());

                // OPTIMIZED: Get all product and unit data in batch
                var allProductIds = allItems.Select(i => i.Product_ID).Distinct().ToList();
                var allUnitIds = allItems.Where(i => i.Unit_ID.HasValue).Select(i => i.Unit_ID!.Value).Distinct().ToList();

                // FIXED: Sequential execution to avoid DbContext threading issues
                var products = allProductIds.Any() ? await _productRepository.ReadOnlyRespository.GetAsync(filter: p => allProductIds.Contains(p.ID)) : new List<Product>();
                var units = allUnitIds.Any() ? await _unitRepository.ReadOnlyRespository.GetAsync(filter: u => allUnitIds.Contains(u.ID)) : new List<Unit>();

                var productLookup = products.ToDictionary(p => p.ID, p => p);
                var unitLookup = units.ToDictionary(u => u.ID, u => u);

                // OPTIMIZED: Populate all PA data using lookups
                foreach (var childPA in childPAs)
                {
                    if (vendorLookup.TryGetValue(childPA.Vendor_ID, out var vendor))
                    {
                        childPA.VendorName = vendor.Name;
                    }

                    if (itemsByPAId.TryGetValue(childPA.Id, out var paItems))
                    {
                        var itemViewModels = _mapper.Map<List<PurchaseAgreementItemViewModel>>(paItems);

                        foreach (var item in itemViewModels)
                        {
                            if (productLookup.TryGetValue(item.Product_ID, out var product))
                            {
                                item.ProductName = product.Name;
                            }

                            if (item.Unit_ID.HasValue && unitLookup.TryGetValue(item.Unit_ID.Value, out var unit))
                            {
                                item.UnitName = unit.Name;
                            }
                        }

                        childPA.PurchaseAgreementItems = itemViewModels;
                    }
                    else
                    {
                        childPA.PurchaseAgreementItems = new List<PurchaseAgreementItemViewModel>();
                    }
                }

                // Send email to each vendor in the PA group
                foreach (var childPA in childPAs)
                {
                    try
                    {
                        // OPTIMIZED: Use pre-loaded vendor data
                        if (!vendorLookup.TryGetValue(childPA.Vendor_ID, out var vendor))
                        {
                            emailErrors.Add($"VENDOR_EMAIL:UNKNOWN|Không tìm thấy thông tin nhà cung cấp cho PA {childPA.Code}");
                            continue;
                        }

                        // Check if vendor has email
                        if (string.IsNullOrWhiteSpace(vendor.Email))
                        {
                            emailErrors.Add($"VENDOR_EMAIL:{vendor.Email}|Nhà cung cấp {vendor.Name} (Mã: {vendor.Code}) chưa có địa chỉ email");
                            continue;
                        }

                        // Generate email content
                        var emailContent = await GeneratePAEmailContent(childPA, vendor);
                        var subject = $"Hợp đồng mua hàng - {childPA.Code}";

                        // Send email
                        await _emailService.SendEmailAsync(vendor.Email, subject, emailContent);
                        emailsSent++;

                        _logger.LogInformation($"Successfully sent PA email to vendor {vendor.Name} ({vendor.Email}) for PA {childPA.Code}");
                    }
                    catch (Exception ex)
                    {
                        var vendorEmail = vendorLookup.TryGetValue(childPA.Vendor_ID, out var vendor) ? vendor.Email : "UNKNOWN";
                        emailErrors.Add($"VENDOR_EMAIL:{vendorEmail}|Lỗi gửi email cho {childPA.VendorName}: {ex.Message}");
                        _logger.LogError(ex, $"Error sending PA email to vendor {childPA.Vendor_ID} for PA {childPA.Code}");
                    }
                }
            }
            catch (Exception ex)
            {
                emailErrors.Add($"Lỗi chung khi gửi email: {ex.Message}");
                _logger.LogError(ex, $"General error sending emails for PA group {groupCode}");
            }

            return (emailsSent, emailErrors);
        }

        /// <summary>
        /// Process email results and add appropriate success/error messages
        /// </summary>
        /// <param name="ack">Acknowledgement object to add messages to</param>
        /// <param name="emailResult">Email sending results</param>
        /// <param name="operationType">Type of operation (e.g., "gửi", "gửi lại")</param>
        /// <param name="groupCode">PA group code (optional)</param>
        private void ProcessEmailResults(Acknowledgement ack, (int emailsSent, List<string> emailErrors) emailResult,
            string operationType, string? groupCode = null)
        {
            // Only show success messages if at least some emails were sent
            if (emailResult.emailsSent > 0)
            {
                var successMessages = new List<string>();

                if (!string.IsNullOrEmpty(groupCode))
                {
                    successMessages.Add($"Đã {operationType} PA tổng hợp '{groupCode}' cho nhà cung cấp thành công.");
                }

                successMessages.Add($"Đã {operationType} email thành công tới {emailResult.emailsSent} nhà cung cấp.");
                ack.AddSuccessMessages(successMessages.ToArray());
            }
            else
            {
                // All emails failed - don't show success message, only errors
                var errorMessage = !string.IsNullOrEmpty(groupCode)
                    ? $"Không thể {operationType} email cho PA tổng hợp '{groupCode}' - tất cả email đều thất bại."
                    : $"Không thể {operationType} email - tất cả email đều thất bại.";
                ack.AddMessage(errorMessage);
            }

            // Add email errors as warnings if any
            if (emailResult.emailErrors.Any())
            {
                ack.AddMessage($"Có {emailResult.emailErrors.Count} lỗi khi {operationType} email:");
                foreach (var error in emailResult.emailErrors)
                {
                    ack.AddMessage($"- {error}");
                }
            }
        }

        /// <summary>
        /// Resend emails to vendors for a PA group and update status
        /// </summary>
        /// <param name="groupCode">PA group code</param>
        /// <returns>Result of the operation</returns>
        public async Task<Acknowledgement> ResendEmails(string groupCode)
        {
            var ack = new Acknowledgement();
            try
            {
                if (string.IsNullOrWhiteSpace(groupCode))
                {
                    ack.AddMessage("Mã nhóm không được để trống.");
                    return ack;
                }

                // Check if PA group exists and has appropriate status
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.GroupCode == groupCode && pa.IsActive);

                if (!purchaseAgreements.Any())
                {
                    ack.AddMessage("Không tìm thấy PA tổng hợp với mã nhóm này.");
                    return ack;
                }

                var firstPA = purchaseAgreements.First();
                var currentStatus = Enum.Parse<EPAStatus>(firstPA.Status);

                // Only allow resend for EmailFailed, SendVendor, or PartialEmailSent status
                if (currentStatus != EPAStatus.EmailFailed && currentStatus != EPAStatus.SendVendor && currentStatus != EPAStatus.PartialEmailSent)
                {
                    ack.AddMessage($"Không thể gửi lại email cho PA có trạng thái '{EnumHelper.GetEnumDescriptionByEnum(currentStatus)}'.");
                    return ack;
                }

                // Send emails to vendors
                var emailResult = await SendEmailsToVendors(groupCode);

                // Check email results for logging
                var allEmailsFailed = emailResult.emailErrors.Any() && emailResult.emailsSent == 0;

                // Update PA statuses based on email results using optimized helper method
                await UpdatePAStatusesBasedOnEmailResults(purchaseAgreements, emailResult);

                // Mark operation as failed only if ALL emails failed
                // Partial success is still considered success with warnings
                if (allEmailsFailed)
                {
                    ack.IsSuccess = false;
                }

                if (ack.IsSuccess)
                {
                    // Process email results using common method
                    ProcessEmailResults(ack, emailResult, "gửi lại");

                    // Add status update message for individual PAs
                    if (emailResult.emailsSent > 0)
                    {
                        var successCount = emailResult.emailsSent;
                        var failureCount = emailResult.emailErrors.Count;

                        if (failureCount == 0)
                        {
                            ack.AddSuccessMessages(new[] { $"Đã gửi lại email thành công cho tất cả {successCount} PA. Trạng thái đã được cập nhật thành 'Đã gửi NCC'." });
                        }
                        else
                        {
                            ack.AddSuccessMessages(new[] { $"Đã gửi lại email cho {successCount} PA thành công, {failureCount} PA thất bại. Trạng thái từng PA đã được cập nhật riêng biệt." });
                        }
                    }
                }
                else
                {
                    // Process email results for failed case
                    ProcessEmailResults(ack, emailResult, "gửi lại");
                }
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi gửi lại email PA: {ex.Message}");
                _logger.LogError(ex, "Error resending PA emails for group {GroupCode}", groupCode);
            }
            return ack;
        }

        /// <summary>
        /// Resend email for a single PA
        /// </summary>
        /// <param name="paCode">PA code</param>
        /// <returns>Result of the operation</returns>
        public async Task<Acknowledgement> ResendSinglePAEmail(string paCode)
        {
            var ack = new Acknowledgement();
            try
            {
                if (string.IsNullOrWhiteSpace(paCode))
                {
                    ack.AddMessage("Mã PA không được để trống.");
                    return ack;
                }

                // Get PA by code
                var purchaseAgreements = await _purchaseAgreementRepository.ReadOnlyRespository.GetAsync(
                    filter: pa => pa.Code == paCode && pa.IsActive);

                var purchaseAgreement = purchaseAgreements.FirstOrDefault();
                if (purchaseAgreement == null)
                {
                    ack.AddMessage("Không tìm thấy PA với mã này.");
                    return ack;
                }

                var currentStatus = Enum.Parse<EPAStatus>(purchaseAgreement.Status);

                // Only allow resend for EmailFailed, SendVendor, or PartialEmailSent status
                if (currentStatus != EPAStatus.EmailFailed && currentStatus != EPAStatus.SendVendor && currentStatus != EPAStatus.PartialEmailSent)
                {
                    ack.AddMessage($"Không thể gửi lại email cho PA có trạng thái '{EnumHelper.GetEnumDescriptionByEnum(currentStatus)}'.");
                    return ack;
                }

                // Get vendor information
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(filter: v => v.ID == purchaseAgreement.Vendor_ID);
                var vendor = vendors.FirstOrDefault();
                if (vendor == null)
                {
                    ack.AddMessage("Không tìm thấy thông tin nhà cung cấp.");
                    return ack;
                }

                // Check if vendor has email
                if (string.IsNullOrWhiteSpace(vendor.Email))
                {
                    ack.AddMessage($"Nhà cung cấp {vendor.Name} (Mã: {vendor.Code}) chưa có địa chỉ email.");
                    return ack;
                }

                // Get PA details for email - create a complete PA view model with items
                var paItems = await _purchaseAgreementItemRepository.ReadOnlyRespository.GetAsync(
                    filter: i => i.PA_ID == purchaseAgreement.ID
                );

                // Get product and unit info for items
                var productIds = paItems.Select(i => i.Product_ID).Distinct().ToList();
                var unitIds = paItems.Where(i => i.Unit_ID.HasValue).Select(i => i.Unit_ID!.Value).Distinct().ToList();

                var products = new Dictionary<int, Product>();
                var units = new Dictionary<int, Unit>();

                if (productIds.Any())
                {
                    var productList = await _productRepository.ReadOnlyRespository.GetAsync(filter: p => productIds.Contains(p.ID));
                    products = productList.ToDictionary(p => p.ID, p => p);
                }

                if (unitIds.Any())
                {
                    var unitList = await _unitRepository.ReadOnlyRespository.GetAsync(filter: u => unitIds.Contains(u.ID));
                    units = unitList.ToDictionary(u => u.ID, u => u);
                }

                var paViewModel = new PurchaseAgreementViewModel
                {
                    Id = purchaseAgreement.ID,
                    Code = purchaseAgreement.Code,
                    GroupCode = purchaseAgreement.GroupCode,
                    Vendor_ID = purchaseAgreement.Vendor_ID,
                    TotalPrice = purchaseAgreement.TotalPrice,
                    Status = Enum.Parse<EPAStatus>(purchaseAgreement.Status),
                    VendorName = vendor.Name,
                    CreatedDate = purchaseAgreement.CreatedDate,
                    Note = purchaseAgreement.Note,
                    PurchaseAgreementItems = paItems.Select(item => new PurchaseAgreementItemViewModel
                    {
                        Product_ID = item.Product_ID,
                        ProductName = products.TryGetValue(item.Product_ID, out var product) ? product.Name : "",
                        Quantity = item.Quantity,
                        Unit_ID = item.Unit_ID,
                        UnitName = item.Unit_ID.HasValue && units.TryGetValue(item.Unit_ID.Value, out var unit) ? unit.Name : "",
                        Price = item.Price
                    }).ToList()
                };

                // Send email
                try
                {
                    var emailContent = await GeneratePAEmailContent(paViewModel, vendor);
                    var subject = $"Hợp đồng mua hàng - {paCode}";

                    await _emailService.SendEmailAsync(vendor.Email, subject, emailContent);

                    // Update status to SendVendor if it was EmailFailed or PartialEmailSent
                    if (currentStatus == EPAStatus.EmailFailed || currentStatus == EPAStatus.PartialEmailSent)
                    {
                        purchaseAgreement.Status = EPAStatus.SendVendor.ToString();
                        purchaseAgreement.UpdatedDate = DateTime.Now;
                        purchaseAgreement.UpdatedBy = CurrentUserId;

                        await ack.TrySaveChangesAsync(res => res.UpdateAsync(purchaseAgreement), _purchaseAgreementRepository.Repository);
                    }
                    else
                    {
                        // If no database update needed, manually set success to true
                        ack.IsSuccess = true;
                    }

                    if (ack.IsSuccess)
                    {
                        var successMessages = new List<string> { $"Đã gửi lại email thành công cho PA {paCode}." };

                        if (currentStatus == EPAStatus.EmailFailed || currentStatus == EPAStatus.PartialEmailSent)
                        {
                            successMessages.Add("Trạng thái đã được cập nhật thành 'Đã gửi NCC'.");
                        }

                        ack.AddSuccessMessages(successMessages.ToArray());
                        _logger.LogInformation($"Successfully resent email for PA {paCode} to vendor {vendor.Name} ({vendor.Email})");
                    }
                }
                catch (Exception ex)
                {
                    ack.AddMessage($"Lỗi gửi email: {ex.Message}");
                    ack.IsSuccess = false; // Mark as failed when email sending fails
                    _logger.LogError(ex, $"Error sending email for PA {paCode} to vendor {vendor.Email}");
                }
            }
            catch (Exception ex)
            {
                ack.AddMessage($"Lỗi khi gửi lại email PA: {ex.Message}");
                _logger.LogError(ex, "Error resending PA email for PA {PaCode}", paCode);
            }
            return ack;
        }

        /// <summary>
        /// OPTIMIZED: Get cached parsed vendors to avoid repeated parsing
        /// </summary>
        private async Task<List<VendorOptionViewModel>> GetCachedParsedVendors()
        {
            // Check if cache is still valid
            if (_cachedParsedVendors != null && DateTime.Now - _vendorCacheTime < _vendorCacheExpiry)
            {
                return _cachedParsedVendors;
            }

            // Cache expired or not initialized, refresh it
            var allVendorsResponse = await _commonService.GetDataOptionsDropdown("", ECategoryType.Vendor);
            var allVendors = allVendorsResponse.IsSuccess ? allVendorsResponse.Data : new List<KendoDropdownListModel<string>>();

            _cachedParsedVendors = allVendors?
                .Where(vendor => vendor != null && !string.IsNullOrEmpty(vendor.Value) && int.TryParse(vendor.Value, out _))
                .Select(vendor => new VendorOptionViewModel
                {
                    ID = int.Parse(vendor.Value),
                    Name = vendor.Text?.Split('(')[0].Trim() ?? "",
                    Code = vendor.Text?.Contains('(') == true ? vendor.Text.Split('(')[1].Replace(")", "").Trim() : ""
                })
                .ToList() ?? new List<VendorOptionViewModel>();

            _vendorCacheTime = DateTime.Now;
            return _cachedParsedVendors;
        }

        #region PA Export Methods

        /// <summary>
        /// Export PA as PDF
        /// </summary>
        /// <param name="purchaseAgreementId">Purchase Agreement ID</param>
        /// <returns>PDF bytes</returns>
        public async Task<byte[]> ExportPAAsPdf(int purchaseAgreementId)
        {
            try
            {
                var paResult = await GetPurchaseAgreementById(purchaseAgreementId);
                if (!paResult.IsSuccess || paResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PA để xuất PDF");
                }

                var html = await GeneratePAHtml(paResult.Data);
                var printableHtml = PdfHelper.ConvertHtmlToPrintableFormat(html, $"PA {paResult.Data.Code}");

                return Encoding.UTF8.GetBytes(printableHtml);
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPAAsPdf: {ex.Message}");
                throw;
            }
        }
        public async Task<byte[]> ExportVendorDetailSummary(int purchaseAgreementId)
        {
            try
            {
                var paResult = await GetPurchaseAgreementById(purchaseAgreementId);
                if (!paResult.IsSuccess || paResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PA để xuất PDF");
                }

                var html = await GeneratePAVendorDetailSummaryFromPurchaseOrdersHtml(paResult.Data);
                var printableHtml = PdfHelper.ConvertHtmlToPrintableFormat(html, $"PA {paResult.Data.Code}");

                return Encoding.UTF8.GetBytes(printableHtml);
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportVendorDetailSummary: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Export PA as Excel
        /// </summary>
        /// <param name="purchaseAgreementId">Purchase Agreement ID</param>
        /// <returns>Excel bytes</returns>
        public async Task<byte[]> ExportPAAsExcel(int purchaseAgreementId)
        {
            try
            {
                var paResult = await GetPurchaseAgreementById(purchaseAgreementId);
                if (!paResult.IsSuccess || paResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PA để xuất Excel");
                }

                using (var workbook = ExcelHelper.CreateWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add("PA");

                    // Set up the PA header
                    int row = 1;

                    // PA info
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 1), $"HỢP ĐỒNG MUA HÀNG - {paResult.Data.Code}");
                    worksheet.Cell(row, 1).Style.Font.Bold = true;
                    worksheet.Cell(row, 1).Style.Font.FontSize = 16;
                    worksheet.Range(row, 1, row, 3).Merge();
                    row += 2;

                    ExcelHelper.SetCellValue(worksheet.Cell(row, 1), $"Nhà cung cấp: {paResult.Data.VendorName}");
                    row++;
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 1), $"Tổng tiền: {paResult.Data.TotalPrice:N0} VNĐ");
                    row++;
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 1), $"Ngày tạo: {paResult.Data.CreatedDate:dd/MM/yyyy HH:mm}");
                    row += 2;

                    // Items header
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 1), "Sản phẩm");
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 2), "Số lượng");
                    ExcelHelper.SetCellValue(worksheet.Cell(row, 3), "Ghi chú");

                    // Style headers
                    for (int col = 1; col <= 3; col++)
                    {
                        worksheet.Cell(row, col).Style.Font.Bold = true;
                        worksheet.Cell(row, col).Style.Fill.BackgroundColor = XLColor.LightBlue;
                        worksheet.Cell(row, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }
                    row++;

                    // Add items data
                    foreach (var item in paResult.Data.PurchaseAgreementItems)
                    {
                        ExcelHelper.SetCellValue(worksheet.Cell(row, 1), item.ProductName ?? "");
                        worksheet.Cell(row, 2).Value = item.Quantity;
                        worksheet.Cell(row, 2).Style.NumberFormat.Format = "#,##0.00";
                        ExcelHelper.SetCellValue(worksheet.Cell(row, 3), "-"); // Note field placeholder
                        row++;
                    }

                    // Auto-fit columns
                    worksheet.Columns().AdjustToContents();

                    return ExcelHelper.SaveToByteArray(workbook);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPAAsExcel: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// Generate HTML for PA export
        /// </summary>
        /// <param name="paData">PA data</param>
        /// <returns>HTML string</returns>
        private async Task<string> GeneratePAHtml(PurchaseAgreementViewModel paData)
        {
            var html = new StringBuilder();

            html.AppendLine("<div style='font-family: Arial, sans-serif; margin: 20px;'>");
            html.AppendLine($"<h1 style='text-align: center; color: #096b73;'>HỢP ĐỒNG MUA HÀNG</h1>");
            html.AppendLine($"<h2 style='text-align: center; color: #0c98a2;'>{paData.Code}</h2>");

            html.AppendLine("<div style='margin: 20px 0;'>");
            html.AppendLine($"<p><strong>Nhà cung cấp:</strong> {paData.VendorName}</p>");
            html.AppendLine($"<p><strong>Tổng tiền:</strong> {paData.TotalPrice:N0} VNĐ</p>");
            html.AppendLine($"<p><strong>Ngày tạo:</strong> {paData.CreatedDate:dd/MM/yyyy HH:mm}</p>");
            if (!string.IsNullOrEmpty(paData.Note))
            {
                html.AppendLine($"<p><strong>Ghi chú:</strong> {paData.Note}</p>");
            }
            html.AppendLine("</div>");

            html.AppendLine("<h3 style='color: #096b73; border-bottom: 2px solid #096b73; padding-bottom: 5px;'>Danh sách sản phẩm</h3>");
            html.AppendLine("<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr style='background-color: #096b73; color: white;'>");
            html.AppendLine("<th style='border: 1px solid #ddd; padding: 12px; text-align: left;'>Sản phẩm</th>");
            html.AppendLine("<th style='border: 1px solid #ddd; padding: 12px; text-align: center;'>Số lượng</th>");
            html.AppendLine("<th style='border: 1px solid #ddd; padding: 12px; text-align: left;'>Ghi chú</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var item in paData.PurchaseAgreementItems)
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td style='border: 1px solid #ddd; padding: 8px;'>{item.ProductName ?? ""}</td>");
                html.AppendLine($"<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>{item.Quantity:N2}</td>");
                html.AppendLine($"<td style='border: 1px solid #ddd; padding: 8px;'>-</td>"); // Note field placeholder
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");
            html.AppendLine("</div>");

            return html.ToString();
        }

        private async Task<string> GeneratePAVendorDetailSummaryFromPurchaseOrdersHtml(PurchaseAgreementViewModel paData)
        {
            var html = new StringBuilder();

            // Get Purchase Order data for this specific vendor
            var vendorOrderData = await GetPurchaseOrderDataForVendor(paData);

            html.AppendLine("<div style='font-family: Arial, sans-serif; margin: 20px;'>");
            html.AppendLine($"<h1 style='text-align: center; color: #096b73;'>ĐƠN ĐẶT HÀNG NHÀ CUNG CẤP ({paData.VendorName.ToUpper()})</h1>");

            html.AppendLine($"<h3 style='color: #096b73; margin-bottom: 4px;'>Ngày: {paData.CreatedDate.ToString("dd/MM/yyyy")}</h3>");
            // Customer orders information
            if (vendorOrderData.CustomerOrders.Any())
            {
                html.AppendLine("<div style='margin-bottom: 30px;'>");
                html.AppendLine($"<h3 style='color: #096b73; border-bottom: 2px solid #096b73; padding-bottom: 5px;'>Danh sách hàng khách đặt</h3>");

                foreach (var customerOrder in vendorOrderData.CustomerOrders.OrderBy(co => co.CustomerName))
                {
                    html.AppendLine("<div style='margin-bottom: 25px; padding: 15px; border: 1px solid #ccc; background-color: #f5f5f5;'>");

                    // Customer header
                    html.AppendLine($"<h4 style='color: #0c98a2; margin-top: 0;'>Khách hàng: {customerOrder.CustomerName}</h4>");

                    // Customer order items table
                    if (customerOrder.Items.Any())
                    {
                        html.AppendLine("<table style='width: 100%; border-collapse: collapse; margin-top: 15px;'>");
                        html.AppendLine("<thead>");
                        html.AppendLine("<tr style='background-color: #096b73; color: white;'>");
                        html.AppendLine("<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>STT</th>");
                        html.AppendLine("<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Tên sản phẩm</th>");
                        html.AppendLine("<th style='border: 1px solid #ddd; padding: 8px; text-align: center;'>Số lượng</th>");
                        html.AppendLine("<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Đơn vị</th>");
                        html.AppendLine("</tr>");
                        html.AppendLine("</thead>");
                        html.AppendLine("<tbody>");

                        int stt = 1;
                        foreach (var item in customerOrder.Items)
                        {
                            html.AppendLine("<tr>");
                            html.AppendLine($"<td style='border: 1px solid #ddd; padding: 6px; text-align: center;'>{stt}</td>");
                            html.AppendLine($"<td style='border: 1px solid #ddd; padding: 6px;'>{item.ProductName ?? ""}</td>");
                            html.AppendLine($"<td style='border: 1px solid #ddd; padding: 6px; text-align: center;'>{item.Quantity:N2}</td>");
                            html.AppendLine($"<td style='border: 1px solid #ddd; padding: 6px;'>{item.UnitName ?? ""}</td>");
                            html.AppendLine("</tr>");
                            stt++;
                        }

                        html.AppendLine("</tbody>");
                        html.AppendLine("</table>");
                    }

                    html.AppendLine("</div>");
                }


            }

            html.AppendLine("</div>");

            return html.ToString();
        }

        /// <summary>
        /// Get Purchase Order data for a specific vendor from PA data
        /// </summary>
        /// <param name="paData">PA data</param>
        /// <returns>Vendor order data with customer information</returns>
        private async Task<VendorOrderWithCustomers> GetPurchaseOrderDataForVendor(PurchaseAgreementViewModel paData)
        {
            try
            {
                var vendorOrderData = new VendorOrderWithCustomers
                {
                    VendorId = paData.Vendor_ID,
                    VendorName = paData.VendorName ?? "",
                    CustomerOrders = new List<CustomerOrderSummary>()
                };

                if (paData.PurchaseAgreementItems == null || !paData.PurchaseAgreementItems.Any())
                    return vendorOrderData;

                // Get all PA Items with PO_Item_ID_List
                var paItems = await _purchaseAgreementItemRepository.ReadOnlyRespository.GetAsync(
                    filter: item => item.PA_ID == paData.Id && !string.IsNullOrEmpty(item.PO_Item_ID_List)
                );

                if (!paItems.Any())
                    return vendorOrderData;

                // Parse PO Item IDs from PO_Item_ID_List
                var poItemIds = new List<int>();
                foreach (var paItem in paItems)
                {
                    if (!string.IsNullOrEmpty(paItem.PO_Item_ID_List))
                    {
                        var ids = paItem.PO_Item_ID_List.Split(',')
                            .Where(id => int.TryParse(id.Trim(), out _))
                            .Select(id => int.Parse(id.Trim()))
                            .ToList();
                        poItemIds.AddRange(ids);
                    }
                }

                if (!poItemIds.Any())
                    return vendorOrderData;

                // Get Purchase Order Items
                var purchaseOrderItems = await _purchaseOrderItemRepository.ReadOnlyRespository.GetAsync(
                    filter: poi => poItemIds.Contains(poi.ID)
                );

                if (!purchaseOrderItems.Any())
                    return vendorOrderData;

                // Get Purchase Order IDs and fetch Purchase Orders
                var purchaseOrderIds = purchaseOrderItems.Select(poi => poi.PO_ID).Distinct().ToList();
                var purchaseOrders = await _purchaseOrderRepository.ReadOnlyRespository.GetAsync(
                    filter: po => purchaseOrderIds.Contains(po.ID)
                );
                var purchaseOrderLookup = purchaseOrders.ToDictionary(po => po.ID, po => po);

                // Get Customer IDs and fetch Customers
                var customerIds = purchaseOrders.Select(po => po.Customer_ID).Distinct().ToList();
                var customers = await _customerRepository.ReadOnlyRespository.GetAsync(
                    filter: c => customerIds.Contains(c.ID)
                );
                var customerLookup = customers.ToDictionary(c => c.ID, c => c);

                // Get Product IDs and fetch products
                var productIds = purchaseOrderItems.Select(poi => poi.Product_ID).Distinct().ToList();
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    filter: p => productIds.Contains(p.ID)
                );
                var productLookup = products.ToDictionary(p => p.ID, p => p);

                // Get Unit IDs and fetch units
                var unitIds = purchaseOrderItems.Where(poi => poi.Unit_ID.HasValue)
                    .Select(poi => poi.Unit_ID.Value).Distinct().ToList();
                var units = await _unitRepository.ReadOnlyRespository.GetAsync(
                    filter: u => unitIds.Contains(u.ID)
                );
                var unitLookup = units.ToDictionary(u => u.ID, u => u);

                // Group Purchase Order Items by Customer (not by individual orders)
                var customerGroups = purchaseOrderItems
                    .Where(poi => purchaseOrderLookup.ContainsKey(poi.PO_ID))
                    .GroupBy(poi => purchaseOrderLookup[poi.PO_ID].Customer_ID)
                    .ToList();

                foreach (var customerGroup in customerGroups)
                {
                    var customerId = customerGroup.Key;
                    if (!customerLookup.TryGetValue(customerId, out var customer))
                        continue;

                    // Group items by product and unit, then sum quantities
                    var productGroups = customerGroup
                        .GroupBy(poi => new { poi.Product_ID, poi.Unit_ID })
                        .ToList();

                    var customerItems = new List<PurchaseOrderItemSummary>();

                    foreach (var productGroup in productGroups)
                    {
                        var product = productLookup.TryGetValue(productGroup.Key.Product_ID, out var p) ? p : null;
                        var unit = productGroup.Key.Unit_ID.HasValue && unitLookup.TryGetValue(productGroup.Key.Unit_ID.Value, out var u) ? u : null;

                        // Sum quantities for the same product and unit
                        var totalQuantity = productGroup.Sum(poi => poi.Quantity);

                        var itemSummary = new PurchaseOrderItemSummary
                        {
                            ProductName = product?.Name ?? "Unknown Product",
                            Quantity = totalQuantity,
                            UnitName = unit?.Name ?? "",
                            Price = null // Remove price as requested
                        };

                        customerItems.Add(itemSummary);
                    }

                    var customerOrderSummary = new CustomerOrderSummary
                    {
                        CustomerId = customer.ID,
                        CustomerName = customer.Name,
                        Items = customerItems.OrderBy(item => item.ProductName).ToList()
                    };

                    vendorOrderData.CustomerOrders.Add(customerOrderSummary);
                }



                return vendorOrderData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetPurchaseOrderDataForVendor: {ex.Message}");
                return new VendorOrderWithCustomers
                {
                    VendorId = paData.Vendor_ID,
                    VendorName = paData.VendorName ?? "",
                    CustomerOrders = new List<CustomerOrderSummary>()
                };
            }
        }

        #endregion

        #region PAGroup Export Methods (Multiple Files)

        /// <summary>
        /// Export PAGroup as multiple PDF files (one per PA) in a ZIP archive
        /// </summary>
        /// <param name="groupCode">PAGroup code</param>
        /// <returns>ZIP file bytes containing multiple PDF files</returns>
        public async Task<byte[]> ExportPAGroupAsPdf(string groupCode)
        {
            try
            {
                var paGroupResult = await GetPAByGroupCode(groupCode);
                if (!paGroupResult.IsSuccess || paGroupResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PAGroup để xuất PDF");
                }

                using (var memoryStream = new MemoryStream())
                {
                    using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                    {
                        foreach (var pa in paGroupResult.Data.ChildPAs)
                        {
                            var pdfBytes = await ExportPAAsPdf(pa.Id);
                            var fileName = $"PA_{pa.Code}_{DateTime.Now:yyyyMMdd}.html";

                            var zipEntry = archive.CreateEntry(fileName);
                            using (var entryStream = zipEntry.Open())
                            {
                                await entryStream.WriteAsync(pdfBytes, 0, pdfBytes.Length);
                            }
                        }
                    }
                    return memoryStream.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPAGroupAsPdf: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Export PAGroup as multiple Excel files (one per PA) in a ZIP archive
        /// </summary>
        /// <param name="groupCode">PAGroup code</param>
        /// <returns>ZIP file bytes containing multiple Excel files</returns>
        public async Task<byte[]> ExportPAGroupAsExcel(string groupCode)
        {
            try
            {
                var paGroupResult = await GetPAByGroupCode(groupCode);
                if (!paGroupResult.IsSuccess || paGroupResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PAGroup để xuất Excel");
                }

                using (var memoryStream = new MemoryStream())
                {
                    using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                    {
                        foreach (var pa in paGroupResult.Data.ChildPAs)
                        {
                            var excelBytes = await ExportPAAsExcel(pa.Id);
                            var fileName = $"PA_{pa.Code}_{DateTime.Now:yyyyMMdd}.xlsx";

                            var zipEntry = archive.CreateEntry(fileName);
                            using (var entryStream = zipEntry.Open())
                            {
                                await entryStream.WriteAsync(excelBytes, 0, excelBytes.Length);
                            }
                        }
                    }
                    return memoryStream.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPAGroupAsExcel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Export PAGroup as consolidated detail summary (single file with data grouped by vendor)
        /// Data is sourced from Purchase_Order_Item and grouped by vendor through Product_Vendor relationships
        /// </summary>
        /// <param name="groupCode">PAGroup code</param>
        /// <returns>PDF file bytes containing consolidated summary</returns>
        public async Task<byte[]> ExportPAGroupDetailSummary(string groupCode)
        {
            try
            {
                var paGroupResult = await GetPAByGroupCode(groupCode);
                if (!paGroupResult.IsSuccess || paGroupResult.Data == null)
                {
                    throw new Exception("Không thể lấy thông tin PAGroup để xuất PDF");
                }

                using (var memoryStream = new MemoryStream())
                {
                    using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                    {
                        foreach (var pa in paGroupResult.Data.ChildPAs)
                        {
                            var pdfBytes = await ExportVendorDetailSummary(pa.Id);
                            var fileName = $"PA_Chi_Tiet_{pa.VendorName}_{pa.Code}_{DateTime.Now:yyyyMMdd}.html";

                            var zipEntry = archive.CreateEntry(fileName);
                            using (var entryStream = zipEntry.Open())
                            {
                                await entryStream.WriteAsync(pdfBytes, 0, pdfBytes.Length);
                            }
                        }
                    }
                    return memoryStream.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPAGroupDetailSummary: {ex.Message}");
                throw;
            }
        }


        #endregion
    }

    // Helper classes for vendor order summary
    public class VendorOrderSummary
    {
        public int VendorId { get; set; }
        public string VendorName { get; set; } = "";
        public List<PurchaseOrderItemSummary> Items { get; set; } = new List<PurchaseOrderItemSummary>();
        public decimal TotalAmount { get; set; }
    }

    public class PurchaseOrderItemSummary
    {
        public string ProductName { get; set; } = "";
        public decimal Quantity { get; set; }
        public string UnitName { get; set; } = "";
        public decimal? Price { get; set; }
    }

    // Helper classes for vendor order with customer information
    public class VendorOrderWithCustomers
    {
        public int VendorId { get; set; }
        public string VendorName { get; set; } = "";
        public List<CustomerOrderSummary> CustomerOrders { get; set; } = new List<CustomerOrderSummary>();
    }

    public class CustomerOrderSummary
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public List<PurchaseOrderItemSummary> Items { get; set; } = new List<PurchaseOrderItemSummary>();
    }
}
